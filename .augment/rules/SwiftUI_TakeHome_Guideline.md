# 📘 SwiftUI Senior Developer Implementation Guideline

## 🎯 Project Goal
Build a 2–3 screen SwiftUI app showcasing senior-level implementation of Clean Architecture, dependency injection, and professional UI/UX standards — without needing live API integration.

---

## 🧠 Architecture & Code Structure

### ✅ Clean Architecture Layout
Use a scalable structure:
```
/Presentation → SwiftUI Views, ViewModels
/Domain       → Models, Business Logic, UseCases
/Data         → Repository Layer with Mock Data
/Core         → Constants, Helpers, Utilities
/Infrastructure → Dependency Injection, Network, Persistence
```



> Never place business logic inside View files.

---

## 🔌 Dependency Injection

### ✅ Use DI for testability and decoupling
- Inject Repositories and UseCases via ViewModel initializer
- Use `@EnvironmentObject` or custom container if needed

---

## 🗃 Repository Pattern

### ✅ Protocol-based abstraction
```swift
protocol ProductRepository {
    func fetchProducts() async throws -> [Product]
}
```

- Use `MockProductRepository` to simulate API responses
- Use `Task.sleep(nanoseconds:)` to simulate latency

---

## 📱 UI / UX

- Follow iOS Human Interface Guidelines
- Use:
  - `List`, `LazyVStack`, or `ScrollView` for layout
  - `NavigationStack` for screen transitions
  - `ProgressView` for loading
  - Empty and error views

- Reuse views with custom components

---

## 💾 State Management

- Use `@StateObject`, `@ObservedObject`, `@EnvironmentObject`
- Keep all logic inside ViewModel
- Bind views to `@Published` properties for reactivity

---

## 🧪 Testing

- Write unit tests for:
  - ViewModels
  - UseCases
  - Mock repositories

> Use `XCTest` framework for testing logic outside the UI layer.

---

## ⚙️ Project Setup & Conventions

- File naming: `PascalCase.swift`
- Group files by feature module
- Avoid putting multiple types in one file

---

## 📄 README.md Must Include

- Project purpose and tech stack
- Instructions to build and run
- Features implemented
- Screenshots (if available)
- What could be improved if more time was available

---

## ✅ Code Quality Checklist

- ✅ No logic in View struct
- ✅ No direct instantiation of dependencies in View
- ✅ Constants in `Core/Constants.swift`
- ✅ All assets and strings organized
- ✅ Modular reusable Views

---

## 🚫 Avoid These Anti-Patterns

- ❌ Logic in View body
- ❌ Global mutable singletons
- ❌ Unstructured state updates
- ❌ Unscalable file structure
- ❌ Copy-paste code

---

## ✅ Deliverables

- Full SwiftUI Xcode project
- 2–3 screen navigation flow
- Polished UI and user experience
- Professional README
- GitHub repo with clean commits