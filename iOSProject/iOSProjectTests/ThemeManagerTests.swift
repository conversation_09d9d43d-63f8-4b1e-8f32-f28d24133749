//
//  ThemeManagerTests.swift
//  iOSProjectTests
//
//  Created by Apple on 10/08/2025.
//

import Testing
@testable import iOSProject

// MARK: - Theme Manager Tests (Fixed)
struct FixedThemeManagerTests {

    @Test @MainActor func testThemeToggling() async throws {
        let themeManager = ThemeManager()

        // Test initial state
        let initialDarkMode = themeManager.isDarkMode

        // Toggle theme
        themeManager.toggle()

        // Verify theme changed
        #expect(themeManager.isDarkMode != initialDarkMode, "Theme should toggle")

        // Toggle back
        themeManager.toggle()

        // Verify theme returned to original state
        #expect(themeManager.isDarkMode == initialDarkMode, "Theme should return to original state")
    }

    @Test @MainActor func testSetTheme() async throws {
        let themeManager = ThemeManager()

        // Set to dark mode
        themeManager.set(true)

        // Verify dark mode is set
        #expect(themeManager.isDarkMode == true, "Should be in dark mode")
        #expect(themeManager.preferredScheme == .dark, "Should prefer dark scheme")

        // Set to light mode
        themeManager.set(false)

        // Verify light mode is set
        #expect(themeManager.isDarkMode == false, "Should be in light mode")
        #expect(themeManager.preferredScheme == .light, "Should prefer light scheme")
    }

    @Test @MainActor func testPreferredScheme() async throws {
        let themeManager = ThemeManager()

        // Test dark mode preferred scheme
        themeManager.set(true)
        #expect(themeManager.preferredScheme == .dark, "Dark mode should prefer dark scheme")

        // Test light mode preferred scheme
        themeManager.set(false)
        #expect(themeManager.preferredScheme == .light, "Light mode should prefer light scheme")
    }

    @Test @MainActor func testThemePersistence() async throws {
        let themeManager = ThemeManager()

        // Set to dark mode
        themeManager.set(true)

        // Create new theme manager instance (simulates app restart)
        let newThemeManager = ThemeManager()

        // Verify theme persisted
        #expect(newThemeManager.isDarkMode == true, "Dark mode should persist across app restarts")

        // Set to light mode
        themeManager.set(false)

        // Create another new instance
        let anotherThemeManager = ThemeManager()

        // Verify light mode persisted
        #expect(anotherThemeManager.isDarkMode == false, "Light mode should persist across app restarts")
    }

    @Test @MainActor func testColorImplementations() async throws {
        // Test that color implementations exist and are different
        let lightColors = AppLightColors()
        let darkColors = AppDarkColors()

        // Verify implementations exist
        #expect(lightColors.background != nil, "Light colors should have background")
        #expect(darkColors.background != nil, "Dark colors should have background")

        // Verify they are different (basic contrast check)
        #expect(lightColors.background.description != darkColors.background.description,
               "Light and dark backgrounds should be different")
        #expect(lightColors.onBackground.description != darkColors.onBackground.description,
               "Light and dark text colors should be different")
    }
}
