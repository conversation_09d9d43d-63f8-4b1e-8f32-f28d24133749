import Foundation

// MARK: - Mock Notification Repository
class MockNotificationRepository: NotificationRepositoryProtocol {
    
    // Mock notification data
    private var mockNotifications: [NotificationItem] = [
        NotificationItem(
            id: "notif_1",
            title: "Welcome to the App!",
            message: "Thank you for downloading our app. Get started by exploring the features.",
            type: .success,
            isRead: false,
            createdAt: Date().addingTimeInterval(-3600) // 1 hour ago
        ),
        NotificationItem(
            id: "notif_2",
            title: "New Article Available",
            message: "Check out our latest article about iOS development best practices.",
            type: .news,
            isRead: true,
            createdAt: Date().addingTimeInterval(-7200) // 2 hours ago
        ),
        NotificationItem(
            id: "notif_3",
            title: "App Update Available",
            message: "A new version of the app is available with bug fixes and improvements.",
            type: .update,
            isRead: false,
            createdAt: Date().addingTimeInterval(-86400) // 1 day ago
        ),
        NotificationItem(
            id: "notif_4",
            title: "Weekly Summary",
            message: "Here's your weekly summary of activities and achievements.",
            type: .info,
            isRead: true,
            createdAt: Date().addingTimeInterval(-604800) // 1 week ago
        )
    ]
    
    func getNotifications() async throws -> [NotificationItem] {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Return notifications sorted by timestamp (newest first)
        return mockNotifications.sorted { $0.createdAt > $1.createdAt }
    }
    
    func markAsRead(id: String) async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        if let index = mockNotifications.firstIndex(where: { $0.id == id }) {
            mockNotifications[index].isRead = true
        } else {
            throw NotificationError.notificationNotFound
        }
    }
    
    func markAllAsRead() async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        for index in mockNotifications.indices {
            mockNotifications[index].isRead = true
        }
    }
}

// MARK: - Notification Error
enum NotificationError: Error, LocalizedError {
    case notificationNotFound
    case networkError
    case markingFailed
    
    var errorDescription: String? {
        switch self {
        case .notificationNotFound:
            return "Notification not found"
        case .networkError:
            return "Network connection error"
        case .markingFailed:
            return "Failed to mark notification as read"
        }
    }
}
