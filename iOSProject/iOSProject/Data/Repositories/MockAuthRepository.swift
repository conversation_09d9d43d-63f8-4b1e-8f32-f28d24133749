import Foundation

// MARK: - Mock Auth Repository
class MockAuthRepository: AuthRepositoryProtocol {
    
    // Mock user data
    private let mockUser = User(
        id: "mock_user_123",
        email: "<EMAIL>",
        name: "<PERSON>",
        profileImageURL: nil
    )
    
    func login(email: String, password: String) async throws -> User {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Simple mock validation
        if email.isEmpty || password.isEmpty {
            throw AuthError.invalidCredentials
        }
        
//        if email == "<EMAIL>" && password == "password123" {
//            return mockUser
//        } else {
//            throw AuthError.invalidCredentials
//        }
        
        return mockUser
    }
    
    func logout() async throws {
        // Simulate network delay
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        // Mock logout - always succeeds
    }
    
    func getCurrentUser() async -> User? {
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        // Return mock user if "logged in"
        return mockUser
    }
}

// MARK: - Auth Error
enum AuthError: Error, LocalizedError {
    case invalidCredentials
    case networkError
    case userNotFound
    
    var errorDescription: String? {
        switch self {
        case .invalidCredentials:
            return "Invalid email or password"
        case .networkError:
            return "Network connection error"
        case .userNotFound:
            return "User not found"
        }
    }
}
