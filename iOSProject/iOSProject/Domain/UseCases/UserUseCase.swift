import Foundation

// MARK: - User Use Case
class UserUseCase: UserUseCaseProtocol {
    private let repository: UserRepositoryProtocol
    
    init(repository: UserRepositoryProtocol) {
        self.repository = repository
    }
    
    func getUser(id: String) async throws -> User {
        // Business logic validation
        guard !id.isEmpty else {
            throw UserUseCaseError.emptyUserId
        }
        
        // Delegate to repository
        return try await repository.getUser(id: id)
    }
    
    func updateUser(_ user: User) async throws -> User {
        // Business logic validation
        guard !user.id.isEmpty else {
            throw UserUseCaseError.emptyUserId
        }
        
        guard !user.email.isEmpty else {
            throw UserUseCaseError.emptyEmail
        }
        
        guard !user.name.isEmpty else {
            throw UserUseCaseError.emptyName
        }
        
        guard isValidEmail(user.email) else {
            throw UserUseCaseError.invalidEmailFormat
        }
        
        // Delegate to repository
        return try await repository.updateUser(user)
    }
    
    func getFavourites() async throws -> [String] {
        // Delegate to repository
        return try await repository.getFavourites()
    }
    
    func toggleFavourite(itemId: String) async throws {
        // Business logic validation
        guard !itemId.isEmpty else {
            throw UserUseCaseError.emptyItemId
        }
        
        // Get current favourites
        let currentFavourites = try await repository.getFavourites()
        
        // Toggle logic
        if currentFavourites.contains(itemId) {
            try await repository.removeFromFavourites(itemId: itemId)
        } else {
            try await repository.addToFavourites(itemId: itemId)
        }
    }
    
    // MARK: - Private Helper Methods
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// MARK: - User Use Case Error
enum UserUseCaseError: Error, LocalizedError {
    case emptyUserId
    case emptyEmail
    case emptyName
    case emptyItemId
    case invalidEmailFormat
    
    var errorDescription: String? {
        switch self {
        case .emptyUserId:
            return "User ID cannot be empty"
        case .emptyEmail:
            return "Email cannot be empty"
        case .emptyName:
            return "Name cannot be empty"
        case .emptyItemId:
            return "Item ID cannot be empty"
        case .invalidEmailFormat:
            return "Please enter a valid email address"
        }
    }
}
