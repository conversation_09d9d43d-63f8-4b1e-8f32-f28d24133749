import Foundation

// MARK: - Auth Use Case
class AuthUseCase: AuthUseCaseProtocol {
    private let repository: AuthRepositoryProtocol
    
    init(repository: AuthRepositoryProtocol) {
        self.repository = repository
    }
    
    func login(email: String, password: String) async throws -> User {
        // Business logic validation
        guard !email.isEmpty else {
            throw AuthUseCaseError.emptyEmail
        }
        
        guard !password.isEmpty else {
            throw AuthUseCaseError.emptyPassword
        }
        
        guard isValidEmail(email) else {
            throw AuthUseCaseError.invalidEmailFormat
        }
        
        guard password.count >= 6 else {
            throw AuthUseCaseError.passwordTooShort
        }
        
        // Delegate to repository
        return try await repository.login(email: email, password: password)
    }
    
    func logout() async throws {
        // Delegate to repository
        try await repository.logout()
    }
    
    func getCurrentUser() async -> User? {
        // Delegate to repository
        return await repository.getCurrentUser()
    }
    
    // MARK: - Private Helper Methods
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// MARK: - Auth Use Case Error
enum AuthUseCaseError: Error, LocalizedError {
    case emptyEmail
    case emptyPassword
    case invalidEmailFormat
    case passwordTooShort
    
    var errorDescription: String? {
        switch self {
        case .emptyEmail:
            return "Email cannot be empty"
        case .emptyPassword:
            return "Password cannot be empty"
        case .invalidEmailFormat:
            return "Please enter a valid email address"
        case .passwordTooShort:
            return "Password must be at least 6 characters long"
        }
    }
}
