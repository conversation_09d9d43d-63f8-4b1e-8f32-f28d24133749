import Foundation

// MARK: - Notification Use Case
class NotificationUseCase: NotificationUseCaseProtocol {
    private let repository: NotificationRepositoryProtocol
    
    init(repository: NotificationRepositoryProtocol) {
        self.repository = repository
    }
    
    func getNotifications() async throws -> [NotificationItem] {
        // Delegate to repository
        let notifications = try await repository.getNotifications()
        
        // Business logic: Sort notifications by timestamp (newest first)
        return notifications.sorted { $0.createdAt > $1.createdAt }
    }
    
    func markAsRead(id: String) async throws {
        // Business logic validation
        guard !id.isEmpty else {
            throw NotificationUseCaseError.emptyNotificationId
        }
        
        // Delegate to repository
        try await repository.markAsRead(id: id)
    }
    
    func markAllAsRead() async throws {
        // Delegate to repository
        try await repository.markAllAsRead()
    }
    
    // MARK: - Additional Business Logic Methods
    func getUnreadNotifications() async throws -> [NotificationItem] {
        let allNotifications = try await getNotifications()
        return allNotifications.filter { !$0.isRead }
    }
    
    func getUnreadCount() async throws -> Int {
        let unreadNotifications = try await getUnreadNotifications()
        return unreadNotifications.count
    }
    
    func getNotificationsByType(_ type: NotificationType) async throws -> [NotificationItem] {
        let allNotifications = try await getNotifications()
        return allNotifications.filter { $0.type == type }
    }
    
    func getRecentNotifications(limit: Int = 10) async throws -> [NotificationItem] {
        guard limit > 0 else {
            throw NotificationUseCaseError.invalidLimit
        }
        
        let allNotifications = try await getNotifications()
        return Array(allNotifications.prefix(limit))
    }
}

// MARK: - Notification Use Case Error
enum NotificationUseCaseError: Error, LocalizedError {
    case emptyNotificationId
    case invalidLimit
    
    var errorDescription: String? {
        switch self {
        case .emptyNotificationId:
            return "Notification ID cannot be empty"
        case .invalidLimit:
            return "Limit must be greater than 0"
        }
    }
}
