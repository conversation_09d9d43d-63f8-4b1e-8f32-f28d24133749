//
//  MockNewsUseCase.swift
//  iOSProject
//
//  Created by Apple on 11/08/2025.
//

import Foundation

// MARK: - Mock News Use Case

class MockNewsUseCase: NewsUseCaseProtocol {
    func getNews() async throws -> [NewsItem] {
        // Simulate realistic network delay (200ms)
        try await Task.sleep(nanoseconds: 200000000)

        return [
            NewsItem(
                id: "1",
                title: "Breaking: New Technology Breakthrough",
                description: "Scientists have made a significant breakthrough in quantum computing that could revolutionize the tech industry.",
                content: "Full article content here...",
                imageURL: nil,
                author: "Tech Reporter",
                publishedAt: Date().addingTimeInterval(-3600),
                category: .technology
            ),
            NewsItem(
                id: "2",
                title: "Market Update: Stocks Rise",
                description: "Global markets showed positive momentum today as investors responded to favorable economic indicators.",
                content: "Full article content here...",
                imageURL: nil,
                author: "Business Reporter",
                publishedAt: Date().addingTimeInterval(-7200),
                category: .business
            ),
            NewsItem(
                id: "3",
                title: "Health: New Study Results",
                description: "Recent research reveals important findings about nutrition and its impact on long-term health outcomes.",
                content: "Full article content here...",
                imageURL: nil,
                author: "Health Reporter",
                publishedAt: Date().addingTimeInterval(-10800),
                category: .health
            )
        ]
    }

    func getNewsItem(id: String) async throws -> NewsItem {
        let allNews = try await getNews()
        guard let newsItem = allNews.first(where: { $0.id == id }) else {
            throw NewsUseCaseError.newsNotFound
        }
        return newsItem
    }
}

