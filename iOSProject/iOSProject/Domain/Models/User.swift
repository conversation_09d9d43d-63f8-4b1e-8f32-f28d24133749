import Foundation

// MARK: - User Model
struct User: Codable, Identifiable, Equatable {
    let id: String
    let email: String
    let name: String
    let profileImageURL: String?
    let isEmailVerified: Bool
    let createdAt: Date
    let updatedAt: Date
    
    init(
        id: String = UUID().uuidString,
        email: String,
        name: String,
        profileImageURL: String? = nil,
        isEmailVerified: Bool = false,
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.email = email
        self.name = name
        self.profileImageURL = profileImageURL
        self.isEmailVerified = isEmailVerified
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - User Extensions
extension User {
    static let mock = User(
        email: "<EMAIL>",
        name: "<PERSON>",
        profileImageURL: nil,
        isEmailVerified: true
    )
    
    var displayName: String {
        return name.isEmpty ? email : name
    }
    
    var initials: String {
        let components = name.components(separatedBy: " ")
        let firstInitial = components.first?.first?.uppercased() ?? ""
        let lastInitial = components.count > 1 ? components.last?.first?.uppercased() ?? "" : ""
        return firstInitial + lastInitial
    }
}

// MARK: - Authentication State
enum AuthenticationState {
    case authenticated(User)
    case unauthenticated
    case loading
}

// MARK: - Login Credentials
struct LoginCredentials {
    let email: String
    let password: String
    
    init(email: String, password: String) {
        self.email = email
        self.password = password
    }
}

// MARK: - Registration Data
struct RegistrationData {
    let email: String
    let password: String
    let name: String
    let confirmPassword: String
    
    init(email: String, password: String, name: String, confirmPassword: String) {
        self.email = email
        self.password = password
        self.name = name
        self.confirmPassword = confirmPassword
    }
}
