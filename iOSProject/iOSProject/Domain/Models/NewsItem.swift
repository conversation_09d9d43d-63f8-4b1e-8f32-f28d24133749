import Foundation

// MARK: - News Item Model
struct NewsItem: Codable, Identifiable, Equatable {
    let id: String
    let title: String
    let description: String
    let content: String
    let imageURL: String?
    let author: String
    let publishedAt: Date
    let category: NewsCategory
    let tags: [String]
    let isBookmarked: Bool
    
    init(
        id: String = UUID().uuidString,
        title: String,
        description: String,
        content: String,
        imageURL: String? = nil,
        author: String,
        publishedAt: Date = Date(),
        category: NewsCategory,
        tags: [String] = [],
        isBookmarked: Bool = false
    ) {
        self.id = id
        self.title = title
        self.description = description
        self.content = content
        self.imageURL = imageURL
        self.author = author
        self.publishedAt = publishedAt
        self.category = category
        self.tags = tags
        self.isBookmarked = isBookmarked
    }
}

// MARK: - News Category
enum NewsCategory: String, Codable, CaseIterable {
    case technology = "technology"
    case business = "business"
    case finance = "finance"
    case health = "health"
    case sports = "sports"
    case entertainment = "entertainment"
    case general = "general"
    
    var displayName: String {
        switch self {
        case .technology:
            return "Technology"
        case .business:
            return "Business"
        case .finance:
            return "Finance"
        case .health:
            return "Health"
        case .sports:
            return "Sports"
        case .entertainment:
            return "Entertainment"
        case .general:
            return "General"
        }
    }
    
    var color: String {
        switch self {
        case .technology:
            return "7E56D8"
        case .business:
            return "52379E"
        case .finance:
            return "6840C6"
        case .health:
            return "422F7D"
        case .sports:
            return "FF5252"
        case .entertainment:
            return "667085"
        case .general:
            return "101828"
        }
    }
}

// MARK: - News Item Extensions
extension NewsItem {
    static let mockItems: [NewsItem] = [
        NewsItem(
            title: "Analytics that feels like it's from the future",
            description: "Powerful, self-serve product and growth analytics to help you convert, engage, and retain more users.",
            content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
            author: "John Smith",
            category: .technology,
            tags: ["analytics", "future", "technology"]
        ),
        NewsItem(
            title: "Portfolio performance tracking made easy",
            description: "Join 4,000+ companies already growing with our analytics platform.",
            content: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
            author: "Jane Doe",
            category: .finance,
            tags: ["portfolio", "tracking", "finance"]
        ),
        NewsItem(
            title: "Start your free trial today",
            description: "Unlock yourself with powerful analytics and insights.",
            content: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
            author: "Mike Johnson",
            category: .business,
            tags: ["trial", "business", "growth"]
        )
    ]
    
    var formattedPublishedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: publishedAt)
    }
    
    var timeAgo: String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(publishedAt)
        
        if timeInterval < 60 {
            return "Just now"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes) minute\(minutes == 1 ? "" : "s") ago"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours) hour\(hours == 1 ? "" : "s") ago"
        } else {
            let days = Int(timeInterval / 86400)
            return "\(days) day\(days == 1 ? "" : "s") ago"
        }
    }
}
