import Foundation

// MARK: - Feature Item Model
struct FeatureItem: Identifiable, Equatable {
    let id = UUID()
    let icon: String
    let title: String
    let description: String
    
    init(icon: String, title: String, description: String) {
        self.icon = icon
        self.title = title
        self.description = description
    }
}

// MARK: - FeatureItem Mock Data
extension FeatureItem {
    static let mockFeatures: [FeatureItem] = [
        FeatureItem(
            icon: ImageConstants.imgPiechart,
            title: "Access to daily analytics",
            description: "Optimize the way you recover, train, and sleep with daily reporting on mobile and desktop apps."
        ),
        FeatureItem(
            icon: ImageConstants.imgZap,
            title: "Measure recovery",
            description: "The most advanced sleep tracking technology available today."
        ),
        FeatureItem(
            icon: ImageConstants.imgSmartphone,
            title: "Tech that evolves with you",
            description: "Know where your strengths lie and where you can improve."
        ),
        FeatureItem(
            icon: ImageConstants.imgUsers,
            title: "Unrivalled community",
            description: "Join teams in the app with friends, family, and like-minded fitness enthusiasts."
        )
    ]
}

// MARK: - Pricing Plan Model
struct PricingPlan: Identifiable, Equatable {
    let id = UUID()
    let price: String
    let title: String
    let description: String
    let isPopular: Bool
    let features: [String]
    
    init(price: String, title: String, description: String, isPopular: Bool, features: [String]) {
        self.price = price
        self.title = title
        self.description = description
        self.isPopular = isPopular
        self.features = features
    }
}

// MARK: - PricingPlan Mock Data
extension PricingPlan {
    static let mockPlans: [PricingPlan] = [
        PricingPlan(
            price: "$10/mth",
            title: "Basic plan",
            description: "Billed annually.",
            isPopular: true,
            features: [
                "Access to all basic features",
                "Basic reporting and analytics",
                "Up to 10 individual users",
                "20GB individual data each user",
                "Basic chat and email support"
            ]
        ),
        PricingPlan(
            price: "$20/mth",
            title: "Business plan",
            description: "Billed annually.",
            isPopular: false,
            features: [
                "200+ integrations",
                "Advanced reporting",
                "Up to 20 individual users",
                "40GB individual data each user",
                "Priority chat and email support"
            ]
        )
    ]
}

// MARK: - Partner Logo Model
struct PartnerLogo: Identifiable, Equatable {
    let id = UUID()
    let logoName: String
    let companyName: String
    
    init(logoName: String, companyName: String) {
        self.logoName = logoName
        self.companyName = companyName
    }
}

// MARK: - Landing Data
struct LandingData {
    let features: [FeatureItem]
    let pricingPlans: [PricingPlan]
    let partnerLogos: [PartnerLogo]
    
    init(features: [FeatureItem], pricingPlans: [PricingPlan], partnerLogos: [PartnerLogo]) {
        self.features = features
        self.pricingPlans = pricingPlans
        self.partnerLogos = partnerLogos
    }
}

// MARK: - Default Landing Data
extension LandingData {
    static let `default` = LandingData(
        features: [
            FeatureItem(
                icon: ImageConstants.imgPiechart,
                title: "Access to daily analytics",
                description: "Optimize the way you recover, train, and sleep with daily reporting on mobile and desktop apps. Start training smarter, not harder."
            ),
            FeatureItem(
                icon: ImageConstants.imgZap,
                title: "Measure recovery",
                description: "The most advanced sleep tracking technology available today. Measure and track your recovery to unlock your greatest potential."
            ),
            FeatureItem(
                icon: ImageConstants.imgSmartphone,
                title: "Tech that evolves with you",
                description: "Know where your strengths lie and where you can improve. Untitled provides the latest tech with a steady stream of new features."
            ),
            FeatureItem(
                icon: ImageConstants.imgUsers,
                title: "Unrivalled community",
                description: "Join teams in the app with friends, family, and like-minded fitness enthusiasts. Create custom teams based on activities and interests."
            )
        ],
        pricingPlans: [
            PricingPlan(
                price: "$10/mth",
                title: "Basic plan",
                description: "Billed annually.",
                isPopular: true,
                features: [
                    "Access to all basic features",
                    "Basic reporting and analytics",
                    "Up to 10 individual users",
                    "20GB individual data each user",
                    "Basic chat and email support"
                ]
            ),
            PricingPlan(
                price: "$20/mth",
                title: "Business plan",
                description: "Billed annually.",
                isPopular: false,
                features: [
                    "200+ integrations",
                    "Advanced reporting",
                    "Up to 20 individual users",
                    "40GB individual data each user",
                    "Priority chat and email support"
                ]
            )
        ],
        partnerLogos: [
            PartnerLogo(logoName: "apple.logo", companyName: "Apple"),
            PartnerLogo(logoName: "microsoft.logo", companyName: "Microsoft"),
            PartnerLogo(logoName: "google.logo", companyName: "Google"),
            PartnerLogo(logoName: "amazon.logo", companyName: "Amazon"),
            PartnerLogo(logoName: "meta.logo", companyName: "Meta"),
            PartnerLogo(logoName: "netflix.logo", companyName: "Netflix")
        ]
    )
}
