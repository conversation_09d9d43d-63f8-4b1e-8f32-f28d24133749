import Foundation

// MARK: - Navigation UseCase Protocol (Domain Layer)
protocol NavigationUseCase {
    func navigateToMainNavigation()
    func navigateToAuthentication()
    func navigateBack()
    func showSuccessMessage(_ message: String)
    func showErrorMessage(_ message: String)
}

// MARK: - Authentication Navigation UseCase (Specific for Auth flow)
protocol AuthNavigationUseCase {
    func navigateToMainAfterLogin()
    func showLoginSuccess()
    func showLoginError(_ message: String)
}
