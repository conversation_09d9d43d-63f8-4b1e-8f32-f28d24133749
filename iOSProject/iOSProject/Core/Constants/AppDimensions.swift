import SwiftUI

// MARK: - App Dimensions
/// Centralized dimension management matching <PERSON><PERSON><PERSON>'s sizing system
struct AppDimensions {
    
    // MARK: - Base Dimensions
    static let baseWidth: CGFloat = 375.0 // iPhone base width
    static let baseHeight: CGFloat = 812.0 // iPhone base height
    
    // MARK: - Responsive Sizing
    static var screenWidth: CGFloat {
        UIScreen.main.bounds.width
    }
    
    static var screenHeight: CGFloat {
        UIScreen.main.bounds.height
    }
    
    static var scaleFactor: CGFloat {
        screenWidth / baseWidth
    }
    
    // MARK: - Spacing System (matches Flutter)
    static let spacing2: CGFloat = 2
    static let spacing4: CGFloat = 4
    static let spacing8: CGFloat = 8
    static let spacing12: CGFloat = 12
    static let spacing16: CGFloat = 16
    static let spacing20: CGFloat = 20
    static let spacing24: CGFloat = 24
    static let spacing32: CGFloat = 32
    static let spacing44: CGFloat = 44
    static let spacing48: CGFloat = 48
    static let spacing64: CGFloat = 64
    
    // MARK: - Border Radius
    static let radiusSmall: CGFloat = 4
    static let radiusMedium: CGFloat = 8
    static let radiusLarge: CGFloat = 12
    static let radiusXLarge: CGFloat = 16
    static let radiusXXLarge: CGFloat = 20
    
    // MARK: - Component Dimensions
    
    // Button Dimensions
    static let buttonHeight: CGFloat = 44
    static let buttonHeightSmall: CGFloat = 32
    static let buttonHeightLarge: CGFloat = 56
    static let buttonMinWidth: CGFloat = 88
    
    // Input Field Dimensions
    static let inputHeight: CGFloat = 44
    static let inputHeightLarge: CGFloat = 56
    
    // Icon Dimensions
    static let iconSmall: CGFloat = 16
    static let iconMedium: CGFloat = 20
    static let iconLarge: CGFloat = 24
    static let iconXLarge: CGFloat = 32
    static let iconXXLarge: CGFloat = 40
    
    // Avatar Dimensions
    static let avatarSmall: CGFloat = 32
    static let avatarMedium: CGFloat = 40
    static let avatarLarge: CGFloat = 56
    static let avatarXLarge: CGFloat = 80
    
    // Card Dimensions
    static let cardPadding: CGFloat = 16
    static let cardRadius: CGFloat = 12
    static let cardElevation: CGFloat = 4
    
    // AppBar Dimensions
    static let appBarHeight: CGFloat = 56
    static let appBarLeadingPadding: CGFloat = 16
    static let appBarTrailingPadding: CGFloat = 16
    
    // Tab Bar Dimensions
    static let tabBarHeight: CGFloat = 50
    static let tabBarIconSize: CGFloat = 24
    
    // Drawer Dimensions
    static let drawerWidth: CGFloat = 0.66 // 66% of screen width
    static let drawerMaxWidth: CGFloat = 320
    
    // List Item Dimensions
    static let listItemHeight: CGFloat = 56
    static let listItemPadding: CGFloat = 16
    
    // Divider Dimensions
    static let dividerHeight: CGFloat = 1
    static let dividerThick: CGFloat = 2
    
    // Shadow Properties
    static let shadowRadius: CGFloat = 4
    static let shadowOpacity: CGFloat = 0.1
    static let shadowOffset: CGSize = CGSize(width: 0, height: 2)
}

// MARK: - Responsive Extensions
extension CGFloat {
    /// Responsive width based on screen size
    var w: CGFloat {
        return self * AppDimensions.scaleFactor
    }
    
    /// Responsive height based on screen size
    var h: CGFloat {
        return self * AppDimensions.scaleFactor
    }
    
    /// Responsive size (both width and height)
    var r: CGFloat {
        return self * AppDimensions.scaleFactor
    }
}

// MARK: - Dimension Extensions for Int
extension Int {
    var w: CGFloat {
        return CGFloat(self) * AppDimensions.scaleFactor
    }
    
    var h: CGFloat {
        return CGFloat(self) * AppDimensions.scaleFactor
    }
    
    var r: CGFloat {
        return CGFloat(self) * AppDimensions.scaleFactor
    }
}

// MARK: - View Extensions for Spacing
extension View {
    func responsivePadding(_ edges: Edge.Set = .all, _ length: CGFloat) -> some View {
        self.padding(edges, length.h)
    }

    func responsiveFrame(width: CGFloat? = nil, height: CGFloat? = nil, alignment: Alignment = .center) -> some View {
        self.frame(
            width: width?.w,
            height: height?.h,
            alignment: alignment
        )
    }

    func responsiveCornerRadius(_ radius: CGFloat) -> some View {
        self.clipShape(RoundedRectangle(cornerRadius: radius.r))
    }

    func responsiveShadow(
        color: Color = .black,
        radius: CGFloat = AppDimensions.shadowRadius,
        x: CGFloat = AppDimensions.shadowOffset.width,
        y: CGFloat = AppDimensions.shadowOffset.height
    ) -> some View {
        self.shadow(
            color: color.opacity(AppDimensions.shadowOpacity),
            radius: radius.r,
            x: x.w,
            y: y.h
        )
    }
}

// MARK: - Spacing Helpers
struct Spacing {
    static func vertical(_ amount: CGFloat) -> some View {
        Spacer().frame(height: amount.h)
    }
    
    static func horizontal(_ amount: CGFloat) -> some View {
        Spacer().frame(width: amount.w)
    }
}

// MARK: - Safe Area Extensions
extension View {
    func safeAreaPadding(_ edges: Edge.Set = .all) -> some View {
        self.padding(edges, getSafeAreaInsets().top)
    }

    private func getSafeAreaInsets() -> UIEdgeInsets {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIEdgeInsets.zero
        }
        return window.safeAreaInsets
    }
}

// MARK: - Device Type Detection
struct DeviceInfo {
    static var isPhone: Bool {
        UIDevice.current.userInterfaceIdiom == .phone
    }
    
    static var isPad: Bool {
        UIDevice.current.userInterfaceIdiom == .pad
    }
    
    static var isSmallScreen: Bool {
        AppDimensions.screenWidth <= 375
    }
    
    static var isLargeScreen: Bool {
        AppDimensions.screenWidth >= 414
    }
}

// MARK: - Layout Constants
struct LayoutConstants {
    // Maximum content width for large screens
    static let maxContentWidth: CGFloat = 400
    
    // Minimum touch target size (accessibility)
    static let minTouchTarget: CGFloat = 44
    
    // Standard margins
    static let marginSmall: CGFloat = 8
    static let marginMedium: CGFloat = 16
    static let marginLarge: CGFloat = 24
    static let marginXLarge: CGFloat = 32
    
    // Grid spacing
    static let gridSpacing: CGFloat = 16
    static let gridColumns: Int = 2
    
    // Animation durations
    static let animationFast: Double = 0.2
    static let animationMedium: Double = 0.3
    static let animationSlow: Double = 0.5
}

// MARK: - Typography Spacing
struct TypographySpacing {
    // Line heights (matching Flutter)
    static let lineHeightTight: CGFloat = 1.2
    static let lineHeightNormal: CGFloat = 1.25
    static let lineHeightRelaxed: CGFloat = 1.4
    
    // Letter spacing
    static let letterSpacingTight: CGFloat = -0.5
    static let letterSpacingNormal: CGFloat = 0
    static let letterSpacingWide: CGFloat = 0.5
}
