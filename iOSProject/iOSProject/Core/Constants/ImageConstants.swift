import Foundation
import UIKit
import OSLog

// MARK: - Image Constants
/// Centralized image asset management for iOS app
/// Equivalent to <PERSON>lut<PERSON>'s ImageConstant class
struct ImageConstants {
    
    // MARK: - Base Configuration
    private static let basePath = ""
    
    // MARK: - Placeholder Images
    static let placeholder = "placeholder"
    static let imageNotFound = "image_not_found"
    
    // MARK: - Authentication Screen Assets
    static let googleLogo = "GoogleLogo"
    static let appleLogo = "AppleLogo"
    static let headerImgIllustration = "Illustration"

    // MARK: - Home Screen Assets
    static let imgHeroImage = "HeroImage"
    static let email = "img_mail"
    
    // MARK: - Profile Screen Assets
    static let defaultProfileAvatar = "profile_picture"
    static let arrowRightIcon = "img_arrowright"
    static let arrowLeftIcon = "img_arrowleft"
    static let imgIcedit = "img_icedit"
    static let imgIcwallet = "img_icwallet"
    static let profileImagePlaceholder = "img_ellipse"
    static let imgEditImage = "img_edit_image"
    static let imgUser = "img_user"
    static let imgSearch = "img_search"
    static let imgSearchGray900_0c = "img_search_gray_900_0c"
    static let imgHistory = "img_history"
    static let imgComplain = "img_complain"
    static let imgReferral = "img_referral"
    static let imgAboutUs = "img_about_us"
    static let imgSettings = "img_settings"
    static let imgHelpAndSupport = "img_help_and_support"
    static let imgLogout = "img_logout"
    
    // MARK: - Theme & Localization Assets
    static let imgDarkMode = "img_dark_mode"
    static let darkMode = "darkmode_icon"
    
    // MARK: - Navigation Tab Icons
    static let imgHouse = "house.icon"
    static let imgHeart = "heart.icon"
    static let imgNotepad = "notepad.icon"
    static let imgBellsimple = "bell.icon"
    
    // MARK: - Landing Screen Assets
    static let imgPiechart = "img_piechart"
    static let imgZap = "img_zap"
    static let imgSmartphone = "img_smartphone"
    static let imgUsers = "img_users"
    static let imgLogomark = "logo"
    static let imgMenu = "img_menu"
    static let imgArrowrightDeepPurple300 = "img_arrowright_deep_purple_300"
    static let imgAppleLogo = "img_apple_logo"
    static let imgDownloadOnThe = "img_download_on_the"
    static let imgAppStore = "img_app_store"
    static let imgGooglePlayLogo = "google_play_logo"
    static let imgGetItOn = "img_get_it_on"
    static let imgGooglePlay = "img_google_play"
    static let imgBlob = "img_blob"
    static let imgScreenMockupReplaceFill = "img_screen_mockup_replace_fill"
    static let imgIphoneMockup = "img_iphone_mockup"
    static let img941 = "img_941"
    static let imgRight = "img_right"
    static let phoneMockup1 = "phone_mockup1"
    static let phoneMockup2 = "phone_mockup2"
    static let phoneMockup3 = "phone_mockup3"
    static let phoneMockup4 = "phone_mockup4"
    static let imgVector = "img_vector"
    static let imgVectorGreenA700 = "img_vector_green_a700"
    static let imgCompanyLogo = "img_company_logo"
    static let imgVectorBlueA700_01 = "img_vector_blue_a700_01"
    static let imgVectorBlack900 = "img_vector_black_900"
    static let imgVectorGray900_03 = "img_vector_gray_900_03"
    static let imgVectorBlueA400 = "img_vector_blue_a400"
    static let imgScreenMockupReplaceFill529x254 = "img_screen_mockup_replace_fill_529x254"
    static let img941Black900 = "img_941_black_900"
    static let imgRightBlack900 = "img_right_black_900"
    static let imgGooglePlayLogoWhiteA700 = "img_google_play_logo_white_a700"
    static let imgScreenMockupReplaceFill500x240 = "img_screen_mockup_replace_fill_500x240"
    static let imgScreenMockupReplaceFill1 = "img_screen_mockup_replace_fill_1"
    static let imgScreenMockupReplaceFill2 = "img_screen_mockup_replace_fill_2"
    
    // MARK: - Profile Menu Icons
    static let imgIccontactSupport = "img_iccontact_support"
    static let imgIcexchangeMessage = "img_icexchange_message"
    static let imgIcinfo = "img_icinfo"
    static let imgIclogout = "img_iclogout"
    static let imgIcmarketStatus = "img_icmarket_status"
    static let imgIcpledge = "img_icpledge"
    static let imgIcreports = "img_icreports"
    static let imgIcsettings = "img_icsettings"
    static let imgIcticket = "img_icticket"
    static let imgIctimer = "img_ictimer"
    static let imgIcplus = "img_icplus"
    
    // MARK: - Phone Mockup Widget Assets
    static let imgButtons = "img_buttons"
    static let imgDeviceSurround = "img_device_surround"
    static let imgHighlightBand = "img_highlight_band"
    static let imgBackground = "img_background"
    static let imgAntennaBands = "img_antenna_bands"
    static let imgSpeaker = "img_speaker"
    static let imgCamera = "img_camera"
    
    // MARK: - Pricing Card Widget Assets
    static let imgCheckIcon = "img_check_icon"
    static let imgVectors = "img_vectors"
    
    // MARK: - Additional Assets
    static let mailSvg = "mail"
    static let appstoreBanner = "appstore_banner"
    static let playstoreBanner = "playstore_banner"
    static let imgSocialIcon = "img_social_icon"
    static let imgSocialIconBlack900 = "img_social_icon_black_900"
    static let imgIllustration = "img_illustration"
}

// MARK: - Image Loading Helper
extension ImageConstants {
    /// Helper method to safely load images with fallback
    static func safeImageName(_ imageName: String) -> String {
        // In iOS, we can check if image exists in bundle
        if UIImage(named: imageName) != nil {
            return imageName
        } else {
            Logger.imageLoading.warning("Image not found: \(imageName), using placeholder")
            return placeholder
        }
    }
}
