<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_216_34084)">
<rect width="32" height="32" rx="8" fill="white"/>
<rect width="32" height="32" rx="8" fill="url(#paint0_linear_216_34084)"/>
<g filter="url(#filter0_dd_216_34084)">
<circle cx="16" cy="16" r="8" fill="url(#paint1_linear_216_34084)"/>
</g>
<foreignObject x="-5" y="11" width="42" height="26"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.5px);clip-path:url(#bgblur_1_216_34084_clip_path);height:100%;width:100%"></div></foreignObject><g data-figma-bg-blur-radius="5">
<path d="M0 16H32V19.2C32 23.6804 32 25.9206 31.1281 27.6319C30.3611 29.1372 29.1372 30.3611 27.6319 31.1281C25.9206 32 23.6804 32 19.2 32H12.8C8.31958 32 6.07937 32 4.36808 31.1281C2.86278 30.3611 1.63893 29.1372 0.871948 27.6319C0 25.9206 0 23.6804 0 19.2V16Z" fill="white" fill-opacity="0.2"/>
</g>
</g>
<rect x="0.1" y="0.1" width="31.8" height="31.8" rx="7.9" stroke="#D0D5DD" stroke-width="0.2"/>
<defs>
<filter id="filter0_dd_216_34084" x="5" y="6" width="22" height="22" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_216_34084"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_216_34084" result="effect2_dropShadow_216_34084"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_216_34084" result="shape"/>
</filter>
<clipPath id="bgblur_1_216_34084_clip_path" transform="translate(5 -11)"><path d="M0 16H32V19.2C32 23.6804 32 25.9206 31.1281 27.6319C30.3611 29.1372 29.1372 30.3611 27.6319 31.1281C25.9206 32 23.6804 32 19.2 32H12.8C8.31958 32 6.07937 32 4.36808 31.1281C2.86278 30.3611 1.63893 29.1372 0.871948 27.6319C0 25.9206 0 23.6804 0 19.2V16Z"/>
</clipPath><linearGradient id="paint0_linear_216_34084" x1="16" y1="0" x2="16" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D0D5DD"/>
</linearGradient>
<linearGradient id="paint1_linear_216_34084" x1="12" y1="24" x2="20" y2="8" gradientUnits="userSpaceOnUse">
<stop stop-color="#53389E"/>
<stop offset="1" stop-color="#6941C6"/>
</linearGradient>
<clipPath id="clip0_216_34084">
<rect width="32" height="32" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
