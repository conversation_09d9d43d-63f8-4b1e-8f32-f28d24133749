//
//  HomeCTASection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeCTASection: View {
    @Environment(\.appColors) var colors
    let onAppStoreButtonTapped: () -> Void
    let onPlayStoreButtonTapped: () -> Void

    var body: some View {
        VStack(spacing: 32.h) {
            VStack(spacing: 16.h) {
                Text(AppConstants.Strings.startFreeTrial)
                    .display30SemiBold()
                    .foregroundColor(colors.ctaText)
                    .lineSpacing(4)

                Text(AppConstants.Strings.personalPerformanceTracking)
                    .headline18Regular()
                    .foregroundColor(colors.ctaSubtext)
                    .lineSpacing(6)
            }

            // CTA Buttons with transparent background and white borders
            HStack(spacing: 16.h) {
                CTAAppStoreButtonView(type: .appStore) {
                    onAppStoreButtonTapped()
                }

                CTAAppStoreButtonView(type: .playStore) {
                    onPlayStoreButtonTapped()
                }
            }

            // Phone mockup
            GeometryReader { geometry in
                Image(ImageConstants.phoneMockup3)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: geometry.size.width, height: 280.h)
                    .clipped()
            }
            .frame(height: 280.h)
            .frame(maxWidth: .infinity)
        }
        .padding(.horizontal, 16.h)
        .padding(.top, 40.h)
        .background(colors.ctaBackground)
        .clipShape(RoundedRectangle(cornerRadius: 16.h))
        .padding(.horizontal, 16.h)
    }
}

// MARK: - CTA App Store Button View (transparent background with white borders)
struct CTAAppStoreButtonView: View {
    let type: AppStoreButtonType
    let action: () -> Void
    @Environment(\.appColors) var colors

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8.h) {
                // Logo
                Image(type.logoIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(
                        width: type == .playStore ? 28.h : 20.h,
                        height: type == .playStore ? 28.h : 24.h
                    )

                // Text Content using Images (matching Flutter implementation)
                VStack(alignment: .leading, spacing: 2.h) {
                    Image(type.downloadTextImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 8.h)

                    Image(type.storeTextImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 16.h)
                }

                Spacer()
            }
            .padding(.horizontal, 16.h)
            .frame(height: 44.h)
            .background(Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: 7.h)
                    .stroke(colors.ctaText, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: 7.h))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    HomeCTASection(
        onAppStoreButtonTapped: {},
        onPlayStoreButtonTapped: {}
    )
    .attachAllEnvironmentObjects()
}
