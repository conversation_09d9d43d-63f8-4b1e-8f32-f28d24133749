//
//  HomePartnersSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomePartnersSection: View {
    @Environment(\.appColors) var colors

    var body: some View {
        VStack(spacing: 32.h) {
            Text("Official partner of these companies")
                .title16Medium()
                .foregroundColor(colors.secondaryText)

            // Partner Logos - Three rows matching Flutter layout
            VStack(spacing: 24.h) {
                // Row 1: 2 logos
                HStack(spacing: 32.h) {
                    SimplePartnerLogoView(logoPath: ImageConstants.imgVector)
                    SimplePartnerLogoView(logoPath: ImageConstants.imgVectorGreenA700)
                }

                // Row 2: 3 logos
                HStack(spacing: 32.h) {
                    SimplePartnerLogoView(logoPath: ImageConstants.imgCompanyLogo)
                    SimplePartnerLogoView(logoPath: ImageConstants.imgVectorBlueA700_01)
                    SimplePartnerLogoView(logoPath: ImageConstants.imgVectorBlack900)
                }

                // Row 3: 2 logos
                HStack(spacing: 32.h) {
                    SimplePartnerLogoView(logoPath: ImageConstants.imgVectorGray900_03)
                    SimplePartnerLogoView(logoPath: ImageConstants.imgVectorBlueA400)
                }
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 32.h)
        .background(colors.background)
    }
}

// MARK: - Simple Partner Logo View (matching Flutter PartnerLogoWidget)
struct SimplePartnerLogoView: View {
    let logoPath: String

    var body: some View {
        Image(logoPath)
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(maxWidth: 80.h, maxHeight: 48.h)
    }
}

#Preview {
    HomePartnersSection()
        .attachAllEnvironmentObjects()
}
