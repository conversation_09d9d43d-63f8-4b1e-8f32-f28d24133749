//
//  HomeHeroSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeHeroSection: View {
    @Environment(\.appColors) var colors
    let onGetStarted: () -> Void

    var body: some View {
        VStack(spacing: 32.h) {
            VStack(spacing: 24.h) {
                Text(AppConstants.Strings.portfolioPerformanceTitle)
                    .display36SemiBold()
                    .foregroundColor(colors.primaryText)
                    .lineSpacing(4)

                Text(AppConstants.Strings.portfolioPerformanceSubtitle)
                    .headline18Regular()
                    .foregroundColor(colors.secondaryText)
                    .lineSpacing(6)
            }
            .padding(.horizontal, 16.h)

            // App Store Buttons Row
            HStack(spacing: 16.h) {
                AppStoreButtonView(type: .appStore) {
                    onGetStarted()
                }

                AppStoreButtonView(type: .playStore) {
                    onGetStarted()
                }
            }
            .padding(.horizontal, 16.h)

            // Hero Phone Mockup (matching <PERSON><PERSON><PERSON>'s OverflowBox behavior exactly)
            GeometryReader { geometry in
                Image(ImageConstants.phoneMockup1)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: UIScreen.main.bounds.width, height: 380.h)
                    .clipped()
            }
            .frame(height: 380.h)
            .clipped()
        }
        .padding(.vertical, 32.h)
    }
}

#Preview {
    HomeHeroSection(onGetStarted: {})
        .attachAllEnvironmentObjects()
}
