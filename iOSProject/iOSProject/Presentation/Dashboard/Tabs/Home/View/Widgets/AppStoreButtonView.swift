import SwiftUI

// MARK: - App Store Button Type
enum AppStoreButtonType {
    case appStore
    case playStore
    
    var downloadText: String {
        switch self {
        case .appStore:
            return "Download on the"
        case .playStore:
            return "Get it on"
        }
    }
    
    var storeText: String {
        switch self {
        case .appStore:
            return "App Store"
        case .playStore:
            return "Google Play"
        }
    }
    
    var logoIcon: String {
        switch self {
        case .appStore:
            return ImageConstants.imgAppleLogo
        case .playStore:
            return ImageConstants.imgGooglePlayLogo
        }
    }

    var downloadTextImage: String {
        switch self {
        case .appStore:
            return ImageConstants.imgDownloadOnThe
        case .playStore:
            return ImageConstants.imgGetItOn
        }
    }

    var storeTextImage: String {
        switch self {
        case .appStore:
            return ImageConstants.imgAppStore
        case .playStore:
            return ImageConstants.imgGooglePlay
        }
    }
}

// MARK: - App Store Button View
struct AppStoreButtonView: View {
    let type: AppStoreButtonType
    let action: () -> Void
    @Environment(\.appColors) var colors
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8.h) {
                // Logo
                Image(type.logoIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(
                        width: type == .playStore ? 28.h : 20.h,
                        height: type == .playStore ? 28.h : 24.h
                    )

                // Text Content using Images (matching Flutter implementation)
                VStack(alignment: .leading, spacing: 2.h) {
                    Image(type.downloadTextImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 8.h)

                    Image(type.storeTextImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 16.h)
                }

                Spacer()
            }
            .padding(.horizontal, 16.h)
            .frame(height: 44.h)
            .background(.black)
            .overlay(
                RoundedRectangle(cornerRadius: 7.h)
                    .stroke(colors.borderDefault, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: 7.h))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct AppStoreButtonView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            AppStoreButtonView(
                type: .appStore,
                action: { }
            )
            
            AppStoreButtonView(
                type: .playStore,
                action: { }
            )
        }
        .padding()
        .attachAllEnvironmentObjects()
    }
}
