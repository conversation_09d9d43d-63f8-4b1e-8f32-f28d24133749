import SwiftUI
import Combine

// MARK: - Landing View Model
class LandingViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var landingData: LandingData = LandingData.default
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    @Published var newsletterEmail: String = ""
    @Published var isSubscribing: Bool = false

    // MARK: - Dependencies
    private let newsUseCase: NewsUseCaseProtocol

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init(newsUseCase: NewsUseCaseProtocol) {
        self.newsUseCase = newsUseCase
        loadInitialData()
    }

    // MARK: - Public Methods
    func loadInitialData() {
        Task { @MainActor in
            isLoading = true

            // Simulate loading delay
            try? await Task.sleep(nanoseconds: 500_000_000)

            // Load default data (in a real app, this would come from an API)
            landingData = LandingData.default

            isLoading = false
        }
    }

    func refreshData() {
        loadInitialData()
    }

    func appStoreButtonTapped() {
        // Handle App Store button tap
        print("App Store button tapped")
    }

    func playStoreButtonTapped() {
        // Handle Play Store button tap
        print("Play Store button tapped")
    }

    func getStartedTapped() {
        // Handle Get Started button tap
        print("Get Started button tapped")
    }

    func pricingPlanSelected(_ plan: PricingPlan) {
        // Handle pricing plan selection
        print("Pricing plan selected: \(plan.title)")
    }

    func subscribeToNewsletter() {
        guard !newsletterEmail.isEmpty else {
            showErrorMessage("Please enter a valid email address")
            return
        }

        Task { @MainActor in
            isSubscribing = true

            // Simulate newsletter subscription
            try? await Task.sleep(nanoseconds: 1_000_000_000)

            // Reset email field on success
            newsletterEmail = ""
            isSubscribing = false

            // Show success message (in a real app, you might show a toast or alert)
            print("Successfully subscribed to newsletter")
        }
    }

    // MARK: - Private Methods
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}

// MARK: - Other ViewModels (Placeholder implementations)

// ProfileViewModel moved to Presentation/Profile/ProfileViewModel.swift

// FavouriteViewModel moved to Presentation/Favourites/FavouriteView.swift

// NewsViewModel moved to Presentation/News/NewsView.swift

// NotificationViewModel moved to Presentation/Notifications/NotificationView.swift

// MARK: - Mock Use Cases (temporary implementations)

// MockNewsUseCase moved to Presentation/News/NewsView.swift

class MockUserUseCase: UserUseCaseProtocol {
    func getUser(id: String) async throws -> User {
        try await Task.sleep(nanoseconds: 500_000_000)
        return User.mock
    }
    
    func updateUser(_ user: User) async throws -> User {
        try await Task.sleep(nanoseconds: 500_000_000)
        return user
    }
    
    func getFavourites() async throws -> [String] {
        try await Task.sleep(nanoseconds: 500_000_000)
        return ["1", "2", "3"]
    }
    
    func toggleFavourite(itemId: String) async throws {
        try await Task.sleep(nanoseconds: 500_000_000)
    }
}

// MockNotificationUseCase moved to Presentation/Notifications/NotificationView.swift
