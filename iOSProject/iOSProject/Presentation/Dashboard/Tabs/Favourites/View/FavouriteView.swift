import SwiftUI

// MARK: - Favourite View

struct FavouriteView: View {
    @Environment(\.appColors) var colors

    var body: some View {
        GeometryReader { geo in

            let size = geo.size

            MainScrollBody {
                VStack(spacing: 0) {
                    // a placeholder view

                    Text("Favourite View")
                        .font(.headline)
                        .foregroundColor(colors.secondaryText)
                        .padding()
                }
                .frame(width: size.width, height: size.height, alignment: .center)
                .background(colors.background)
            }
        }
    }
}
