import SwiftUI

// MARK: - Notification View

struct NotificationView: View {
    @Environment(\.appColors) var colors

    var body: some View {
        GeometryReader { geo in

            let size = geo.size

            MainScrollBody {
                VStack(spacing: 0) {
                    // a placeholder view

                    Text("Notification View")
                        .font(.headline)
                        .foregroundColor(colors.secondaryText)
                        .padding()
                }
                .frame(width: size.width, height: size.height, alignment: .center)
                .background(colors.background)
            }
        }
    }
}

// MARK: - Preview

struct NotificationView_Previews: PreviewProvider {
    static var previews: some View {
        NotificationView()
            .attachAllEnvironmentObjects()
    }
}
