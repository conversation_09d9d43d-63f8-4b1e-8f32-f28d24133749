import SwiftUI

// MARK: - Main Navigation View

struct DashboardView: View {
   
    @EnvironmentObject private var navigationService: NavigationService
    @EnvironmentObject private var dependencyContainer: DependencyContainer
    @Environment(\.appColors) var colors
    @EnvironmentObject private var coordinator: NavigationCoordinator
    var body: some View {
        GeometryReader { geo in
            let size = geo.size
            ZStack {
                // Main content
                VStack(spacing: 0) {
                    // Tab Content

                    TabView(selection: $coordinator.selectedTab) {
                        ForEach(Tab.allCases) { tab in
                            NavigationStack(path: navigationService.binding(for: tab)) {
                                tab.view(with: dependencyContainer)
                                    .navigationDestination(for: NavigationDestination.self) { $0 }
                            }
                            .tabItem {
                                Image(tab.image)
                                    .renderingMode(.template)
                                    .resizable()
                                Text(tab.title)
                            }
                            .tag(tab)
                        }
                    }
                    .background(colors.background)
                }
                .blur(radius: coordinator.isDrawerOpen ? 1.5 : 0)
                .overlay {
                    // Background blur overlay when drawer is open
                    if coordinator.isDrawerOpen {
                        colors.onSurface.opacity(0.15)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .ignoresSafeArea()
                            .zIndex(1)
                            .onTapGesture {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    coordinator.toggleDrawer()
                                }
                            }
                    }
                }

                SideDrawer(size: size)
            }
            .animation(.easeInOut(duration: 0.3), value: coordinator.isDrawerOpen)
        }
    }

    // Drawer
    private func SideDrawer(size: CGSize) -> some View {
        Group {
            if coordinator.isDrawerOpen {
                ProfileView.build(container: dependencyContainer, isDrawer: true, isDrawerOpen: $coordinator.isDrawerOpen)
                    .frame(width: size.width * 0.66)
                    .frame(maxWidth: .infinity, alignment: .trailing)
                    .transition(.move(edge: .trailing).combined(with: .opacity))
                    .zIndex(2)
                    .ignoresSafeArea()
            }
        }
    }

    // MARK: - Custom App Bar

    private var customAppBar: some View {
        HStack {
            // Logo
            Image(ImageConstants.imgLogomark)
                .resizable()
                .frame(width: 32.h, height: 32.h)

            Spacer()

            // Menu button
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    coordinator.toggleDrawer()
                }
            }) {
                Image(ImageConstants.imgMenu)
                    .resizable()
                    .frame(width: 24.h, height: 24.h)
                    .foregroundColor(colors.onSurface)
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 12.h)
        .background(colors.surface)
        .shadow(color: colors.shadow.opacity(0.5), radius: 2, x: 0, y: 1)
    }
}


// MARK: - Preview
#Preview {
    DashboardView()
        .provideTheme(dark: true)
        .attachAllEnvironmentObjects()
}
