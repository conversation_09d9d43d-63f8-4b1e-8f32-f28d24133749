//
//  TabModel.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import SwiftUI

enum Tab: String, Identifiable, CaseIterable, Hashable {
    case home = "home"
    case favourite = "favourite"
    case news = "news"
    case notification = "notification"

    var id: Self { self }

    var image: String {
        switch self {
        case .home: ImageConstants.imgHouse
        case .favourite: ImageConstants.imgHeart
        case .news: ImageConstants.imgNotepad
        case .notification: ImageConstants.imgBellsimple
        }
    }

    var title: String {
        let title: String = {
            switch self {
            case .home: "Home"
            case .favourite: "Favourite"
            case .news: "News"
            case .notification: "Notification"
            }
        }()

        return title
    }

    var index: Int {
        return Tab.allCases.firstIndex(of: self) ?? 0
    }
}


extension Tab {
    @ViewBuilder @MainActor
    func view(with container: DependencyContainer) -> some View {
        switch self {
        case .home:
            HomeView.build(container: container)
        case .favourite:
            FavouriteView()
        case .news:
            NewsView()
        case .notification:
            NotificationView()

        }
    }
}
