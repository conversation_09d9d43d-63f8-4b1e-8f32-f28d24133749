//
//  iOSProjectApp.swift
//  iOSProject
//
//  Created by Apple on 08/08/2025.
//

import SwiftUI

struct Toast: Equatable, Identifiable {
    let id: UUID = .init()
    var type: ToastStyle
    var title: String?
    var message: String
    var duration: Double = 5
    var withTimer:Bool = true
}
enum ToastStyle {
    case error
    case warning
    case success
    case info
}
extension ToastStyle {
    var themeColor: Color {
        switch self {
        case .error: return Color(hex: "FF5252")
        case .warning: return Color(hex: "FF9800")
        case .info: return Color(hex: "2196F3")
        case .success: return Color(hex: "4CAF50")
        }
    }
    
    var iconFileName: String {
        switch self {
        case .info: return "info.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .success: return "checkmark.circle.fill"
        case .error: return "xmark.circle.fill"
        }
    }
}
