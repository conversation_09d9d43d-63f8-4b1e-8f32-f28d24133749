//
//  View+Extension.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import SwiftUI

extension View {
    func attachAllEnvironmentObjects() -> some View {
        modifier(EnvironmentModifier())
    }

    func hideNavigationBar(_ hide: Bool = true) -> some View {
        toolbar(hide ? .hidden : .visible, for: .tabBar)
            .toolbar(hide ? .hidden : .visible, for: .navigationBar)
            .navigationBarTitle("", displayMode: .inline)
            .navigationBarBackButtonHidden()
    }
}


/// A custom view modifier to rotate a view based on the app's language.
struct LanguageBasedRotation: ViewModifier {
    let inverse: Bool
    @Environment(\.locale) private var locale
    func body(content: Content) -> some View {
        content.rotationEffect(inverse ? locale.identifier == "en" ? .degrees(-180) : .degrees(0)   : locale.identifier == "ar" ? .degrees(-180) : .degrees(0))
    }
}

extension View {
    /// Rotates the view based on the app's language direction.
    /// - Returns: A rotated view based on the app's language.
    func rotateBasedOnLanguage(inverse: Bool = false) -> some View {
        self.modifier(LanguageBasedRotation(inverse: inverse))
    }
}

