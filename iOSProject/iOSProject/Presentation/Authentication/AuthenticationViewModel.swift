import Combine
import SwiftUI

// MARK: - Authentication View Model

@MainActor
class AuthenticationViewModel: ObservableObject {
    // MARK: - Published Properties

    @Published var email: String = "<EMAIL>"
    @Published var password: String = "123456B"
    @Published var isPasswordVisible: Bool = false
    @Published var isLoading: Bool = false
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    @Published var emailError: String?
    @Published var passwordError: String?
    
    // MARK: - Dependencies (Domain Layer Only)

    private let authUseCase: AuthUseCaseProtocol
    private let validationUseCase: ValidationUseCase
    private let authNavigationUseCase: AuthNavigationUseCase
    
    // MARK: - Private Properties

    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization

    init(
        authUseCase: AuthUseCaseProtocol,
        validationUseCase: ValidationUseCase,
        authNavigationUseCase: AuthNavigationUseCase
    ) {
        self.authUseCase = authUseCase
        self.validationUseCase = validationUseCase
        self.authNavigationUseCase = authNavigationUseCase

        setupValidation()
    }
    
    // MARK: - Public Methods

    func loginTapped() {
        guard validateForm() else { return }
        
        Task { @MainActor in
            isLoading = true
            
            do {
                _ = try await authUseCase.login(email: email, password: password)
                // Navigate to main navigation with minimal delay
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                authNavigationUseCase.navigateToMainAfterLogin()
                authNavigationUseCase.showLoginSuccess()

            } catch {
                showErrorMessage(error.localizedDescription)
            }
            
            isLoading = false
        }
    }
    
    func googleLoginTapped() {
        // Implement Google login
        print("Google login tapped")
    }
    
    func appleLoginTapped() {
        // Implement Apple login
        print("Apple login tapped")
    }
    
    func forgotPasswordTapped() {
        // Implement forgot password
        print("Forgot password tapped")
    }
    
    func signUpTapped() {
        // Navigate to sign up screen
        print("Sign up tapped")
    }
    
    func togglePasswordVisibility() {
        isPasswordVisible.toggle()
    }


    
    // MARK: - Private Methods

    private func setupValidation() {
        // Email validation with optimized debouncing
        $email
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] email in
                self?.validateEmail(email)
            }
            .store(in: &cancellables)

        // Password validation with optimized debouncing
        $password
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] password in
                self?.validatePassword(password)
            }
            .store(in: &cancellables)
    }
    
    private func validateEmail(_ email: String) {
        guard !email.isEmpty else {
            emailError = nil
            return
        }
        
        let result = validationUseCase.validateEmail(email)
        emailError = result.errorMessage
    }
    
    private func validatePassword(_ password: String) {
        guard !password.isEmpty else {
            passwordError = nil
            return
        }
        
        let result = validationUseCase.validatePassword(password)
        passwordError = result.errorMessage
    }
    
    private func validateForm() -> Bool {
        let results = validationUseCase.validateCredentials(email: email, password: password)

        emailError = results.email.errorMessage
        passwordError = results.password.errorMessage

        return results.email.isValid && results.password.isValid
    }
    
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}

// MARK: - Mock Implementations (temporary)

class MockAuthUseCase: AuthUseCaseProtocol {
    func login(email: String, password: String) async throws -> User {
        // Simulate realistic authentication delay (300ms)
        try await Task.sleep(nanoseconds: 300_000_000)

        return User.mock
//        // Simulate login validation
//        if email == "<EMAIL>" && password == "password123" {
//            return User.mock
//        } else {
//            throw AuthError.invalidCredentials
//        }
    }
    
    func logout() async throws {
        // Implement logout
    }
    
    func getCurrentUser() async -> User? {
        return nil
    }
}
