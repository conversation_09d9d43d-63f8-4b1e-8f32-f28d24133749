import SwiftUI
import Combine
import OSLog

// MARK: - Navigation Coordinator
class NavigationCoordinator: ObservableObject {
    // MARK: - Published Properties
    @Published var currentView: AppView = .authentication
    @Published var selectedTab: Tab = .home
    @Published var isDrawerOpen: Bool = false
    @AppStorage(.locale) private(set) var locale: String = "en"
    @Published var navigationPath = NavigationPath()
    @Published var presentedSheet: SheetType?
    @Published var showAlert = false
    @Published var alertMessage = ""

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init() {
        
    }
    
    // MARK: - Navigation Methods
    func navigateToMainNavigation() {
        DispatchQueue.main.async {
            self.currentView = .mainNavigation
            self.navigationPath = NavigationPath()
        }
    }
    
    func navigateToAuthentication() {
        DispatchQueue.main.async {
            self.currentView = .authentication
            self.navigationPath = NavigationPath()
        }
    }
    
    func navigateBack() {
        DispatchQueue.main.async {
            if !self.navigationPath.isEmpty {
                self.navigationPath.removeLast()
            }
        }
    }
    
    func pushView(_ view: NavigationDestination) {
        DispatchQueue.main.async {
            self.navigationPath.append(view)
        }
    }
    
    func presentSheet(_ sheet: SheetType) {
        DispatchQueue.main.async {
            self.presentedSheet = sheet
        }
    }
    
    func dismissSheet() {
        DispatchQueue.main.async {
            self.presentedSheet = nil
        }
    }
    
    func showAlert(message: String) {
        DispatchQueue.main.async {
            self.alertMessage = message
            self.showAlert = true
        }
    }
    
    // MARK: - Tab Navigation
    // Note: These methods should be called through the injected AppStateManager
    // The NavigationCoordinator should receive AppStateManager as a dependency
    @MainActor
    func selectTab(_ tab: Tab) {
        // This will be handled by the injected AppStateManager in the view
        selectedTab = tab
    }

    @MainActor
    func toggleDrawer() {
        // This will be handled by the injected AppStateManager in the view
        isDrawerOpen.toggle()
    }

    
    @MainActor
    func updateLocale(_ locale: String) {
        
        withAnimation(.bouncy) {
            self.locale = locale
        }
        
        
    }
    
    // MARK: - Profile Navigation
    func navigateToEditProfile() {
        pushView(.editProfile)
    }
    
    func navigateToAddress() {
        pushView(.address)
    }
    
    func navigateToHistory() {
        pushView(.history)
    }
    
    func navigateToComplain() {
        pushView(.complain)
    }
    
    func navigateToReferral() {
        pushView(.referral)
    }
    
    func navigateToAboutUs() {
        pushView(.aboutUs)
    }
    
    func navigateToSettings() {
        pushView(.settings)
    }
    
    func navigateToHelpSupport() {
        pushView(.helpSupport)
    }
    
    // MARK: - Content Navigation
    func navigateToNewsDetail(_ newsId: String) {
        pushView(.newsDetail(newsId))
    }
    
    func navigateToNotificationDetail(_ notificationId: String) {
        pushView(.notificationDetail(notificationId))
    }
  
}

// MARK: - Navigation Destination
enum NavigationDestination: Hashable {
    case editProfile
    case address
    case history
    case complain
    case referral
    case aboutUs
    case settings
    case helpSupport
    case newsDetail(String)
    case notificationDetail(String)
}

// MARK: - Sheet Type
enum SheetType: Identifiable {
    case editProfile
    case imageEditor
    case languageSelector
    case themeSelector
    
    var id: String {
        switch self {
        case .editProfile: return "editProfile"
        case .imageEditor: return "imageEditor"
        case .languageSelector: return "languageSelector"
        case .themeSelector: return "themeSelector"
        }
    }
}

// MARK: - App View
enum AppView {
    case authentication
    case mainNavigation
}

// MARK: - Navigation View Builder
extension NavigationDestination: View {
    var body: some View {
        switch self {
        case .editProfile:
            EditProfileView()
        case .address:
            AddressView()
        case .history:
            HistoryView()
        case .complain:
            ComplainView()
        case .referral:
            ReferralView()
        case .aboutUs:
            AboutUsView()
        case .settings:
            SettingsView()
        case .helpSupport:
            HelpSupportView()
        case .newsDetail(let newsId):
            NewsDetailView(newsId: newsId)
        case .notificationDetail(let notificationId):
            NotificationDetailView(notificationId: notificationId)
        }
    }
    
    @ViewBuilder
    func sheetView(for sheet: SheetType) -> some View {
        switch sheet {
        case .editProfile:
            EditProfileView()
        case .imageEditor:
            ImageEditorView()
        case .languageSelector:
            LanguageSelectorView()
        case .themeSelector:
            ThemeSelectorView()
        }
    }
}

// MARK: - Placeholder Views
struct EditProfileView: View {
    var body: some View {
        Text("Edit Profile")
            .navigationTitle("Edit Profile")
    }
}

struct AddressView: View {
    var body: some View {
        Text("Address")
            .navigationTitle("Address")
    }
}

struct HistoryView: View {
    var body: some View {
        Text("History")
            .navigationTitle("History")
    }
}

struct ComplainView: View {
    var body: some View {
        Text("Complain")
            .navigationTitle("Complain")
    }
}

struct ReferralView: View {
    var body: some View {
        Text("Referral")
            .navigationTitle("Referral")
    }
}

struct AboutUsView: View {
    var body: some View {
        Text("About Us")
            .navigationTitle("About Us")
    }
}

struct SettingsView: View {
    var body: some View {
        Text("Settings")
            .navigationTitle("Settings")
    }
}

struct HelpSupportView: View {
    var body: some View {
        Text("Help & Support")
            .navigationTitle("Help & Support")
    }
}

struct NewsDetailView: View {
    let newsId: String
    
    var body: some View {
        Text("News Detail: \(newsId)")
            .navigationTitle("News Detail")
    }
}

struct NotificationDetailView: View {
    let notificationId: String
    
    var body: some View {
        Text("Notification Detail: \(notificationId)")
            .navigationTitle("Notification Detail")
    }
}

struct ImageEditorView: View {
    var body: some View {
        Text("Image Editor")
    }
}

struct LanguageSelectorView: View {
    var body: some View {
        Text("Language Selector")
    }
}

struct ThemeSelectorView: View {
    var body: some View {
        Text("Theme Selector")
    }
}
