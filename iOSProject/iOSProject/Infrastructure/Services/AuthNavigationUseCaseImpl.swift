import Foundation

// MARK: - Auth Navigation UseCase Implementation
class AuthNavigationUseCaseImpl: AuthNavigationUseCase {
    private let navigationService: NavigationServiceProtocol
    
    init(navigationService: NavigationServiceProtocol) {
        self.navigationService = navigationService
    }
    
    func navigateToMainAfterLogin() {
        navigationService.navigateToMainNavigation()
    }
    
    func showLoginSuccess() {
        navigationService.showToast(.init(type: .success, message: "Welcome back"))
    }
    
    func showLoginError(_ message: String) {
        navigationService.showToast(.init(type: .error, message: message))
    }
}
