import 'package:bloc_test/bloc_test.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_project/domain/entities/user_entity.dart';
import 'package:flutter_project/domain/failures/auth_failure.dart';
import 'package:flutter_project/domain/usecases/auth/apple_login_usecase.dart';
import 'package:flutter_project/domain/usecases/auth/google_login_usecase.dart';
import 'package:flutter_project/domain/usecases/auth/login_usecase.dart';
import 'package:flutter_project/presentation/authentication_screen/bloc/authentication_bloc.dart';
import 'package:flutter_project/presentation/authentication_screen/models/authentication_model.dart';
import 'package:flutter_test/flutter_test.dart';

// Mock use cases for testing
class MockLoginUseCase implements LoginUseCase {
  @override
  Future<Either<AuthFailure, UserEntity>> call(LoginParams params) async {
    // Simulate validation and authentication
    if (params.email.isEmpty) {
      return Left(ValidationFailure({'email': 'Email is required'}));
    }
    if (!params.email.contains('@')) {
      return Left(ValidationFailure({'email': 'Please enter a valid email'}));
    }
    if (params.password.isEmpty) {
      return Left(ValidationFailure({'password': 'Password is required'}));
    }
    if (params.password.length < 6) {
      return Left(ValidationFailure({'password': 'Password must be at least 6 characters'}));
    }

    return Right(UserEntity(id: '1', name: 'Test User', email: params.email));
  }
}

class MockGoogleLoginUseCase implements GoogleLoginUseCase {
  @override
  Future<Either<AuthFailure, UserEntity>> call() async {
    return Right(UserEntity(id: '1', name: 'Google User', email: '<EMAIL>'));
  }
}

class MockAppleLoginUseCase implements AppleLoginUseCase {
  @override
  Future<Either<AuthFailure, UserEntity>> call() async {
    return Right(UserEntity(id: '1', name: 'Apple User', email: '<EMAIL>'));
  }
}

void main() {
  group('AuthenticationBloc', () {
    late AuthenticationBloc authenticationBloc;
    late MockLoginUseCase mockLoginUseCase;
    late MockGoogleLoginUseCase mockGoogleLoginUseCase;
    late MockAppleLoginUseCase mockAppleLoginUseCase;

    setUp(() {
      mockLoginUseCase = MockLoginUseCase();
      mockGoogleLoginUseCase = MockGoogleLoginUseCase();
      mockAppleLoginUseCase = MockAppleLoginUseCase();

      authenticationBloc = AuthenticationBloc(
        AuthenticationState(authenticationModel: AuthenticationModel()),
        loginUseCase: mockLoginUseCase,
        googleLoginUseCase: mockGoogleLoginUseCase,
        appleLoginUseCase: mockAppleLoginUseCase,
      );
    });

    tearDown(() {
      authenticationBloc.close();
    });

    group('Email Validation', () {
      blocTest<AuthenticationBloc, AuthenticationState>(
        'should clear email error when user starts typing',
        build: () => authenticationBloc,
        act: (bloc) => bloc.add(EmailChangedEvent('<EMAIL>')),
        expect: () => [
          isA<AuthenticationState>()
              .having((state) => state.email, 'email', '<EMAIL>')
              .having((state) => state.emailError, 'emailError', null)
              .having((state) => state.showError, 'showError', false),
        ],
      );

      blocTest<AuthenticationBloc, AuthenticationState>(
        'should show error for empty email on login',
        build: () => authenticationBloc,
        seed: () => AuthenticationState(
          emailController: TextEditingController(),
          passwordController: TextEditingController()..text = 'password123',
          authenticationModel: AuthenticationModel(),
        ),
        act: (bloc) => bloc.add(LoginButtonTappedEvent()),
        expect: () => [
          isA<AuthenticationState>()
              .having((state) => state.emailError, 'emailError', 'Email is required')
              .having((state) => state.passwordError, 'passwordError', null),
        ],
      );

      blocTest<AuthenticationBloc, AuthenticationState>(
        'should show error for invalid email format on login',
        build: () => authenticationBloc,
        seed: () => AuthenticationState(
          emailController: TextEditingController()..text = 'invalid-email',
          passwordController: TextEditingController()..text = 'password123',
          authenticationModel: AuthenticationModel(),
        ),
        act: (bloc) => bloc.add(LoginButtonTappedEvent()),
        expect: () => [
          isA<AuthenticationState>()
              .having((state) => state.emailError, 'emailError', 'Please enter a valid email address')
              .having((state) => state.passwordError, 'passwordError', null),
        ],
      );
    });

    group('Password Validation', () {
      blocTest<AuthenticationBloc, AuthenticationState>(
        'should clear password error when user starts typing',
        build: () => authenticationBloc,
        act: (bloc) => bloc.add(PasswordChangedEvent('newpassword')),
        expect: () => [
          isA<AuthenticationState>()
              .having((state) => state.password, 'password', 'newpassword')
              .having((state) => state.passwordError, 'passwordError', null)
              .having((state) => state.showError, 'showError', false),
        ],
      );

      blocTest<AuthenticationBloc, AuthenticationState>(
        'should show error for empty password on login',
        build: () => authenticationBloc,
        seed: () => AuthenticationState(
          emailController: TextEditingController()..text = '<EMAIL>',
          passwordController: TextEditingController(),
          authenticationModel: AuthenticationModel(),
        ),
        act: (bloc) => bloc.add(LoginButtonTappedEvent()),
        expect: () => [
          isA<AuthenticationState>()
              .having((state) => state.emailError, 'emailError', null)
              .having((state) => state.passwordError, 'passwordError', 'Password is required'),
        ],
      );

      blocTest<AuthenticationBloc, AuthenticationState>(
        'should show error for short password on login',
        build: () => authenticationBloc,
        seed: () => AuthenticationState(
          emailController: TextEditingController()..text = '<EMAIL>',
          passwordController: TextEditingController()..text = '123',
          authenticationModel: AuthenticationModel(),
        ),
        act: (bloc) => bloc.add(LoginButtonTappedEvent()),
        expect: () => [
          isA<AuthenticationState>()
              .having((state) => state.emailError, 'emailError', null)
              .having((state) => state.passwordError, 'passwordError', 'Password must be at least 6 characters'),
        ],
      );
    });

    group('Login Process', () {
      blocTest<AuthenticationBloc, AuthenticationState>(
        'should start loading with button animation and complete login with valid credentials',
        build: () => authenticationBloc,
        seed: () => AuthenticationState(
          emailController: TextEditingController()..text = '<EMAIL>',
          passwordController: TextEditingController()..text = 'password123',
          authenticationModel: AuthenticationModel(),
        ),
        act: (bloc) => bloc.add(LoginButtonTappedEvent()),
        expect: () => [
          // Start loading with button animation
          isA<AuthenticationState>()
              .having((state) => state.isLoading, 'isLoading', true)
              .having((state) => state.emailError, 'emailError', null)
              .having((state) => state.passwordError, 'passwordError', null)
              .having((state) => state.isButtonAnimating, 'isButtonAnimating', true)
              .having((state) => state.buttonAnimationPhase, 'buttonAnimationPhase', ButtonAnimationPhase.transitioningToCircular),
          // Button reaches circular state
          isA<AuthenticationState>().having((state) => state.buttonAnimationPhase, 'buttonAnimationPhase', ButtonAnimationPhase.circular),
          // Button starts transitioning back to rectangular
          isA<AuthenticationState>().having(
            (state) => state.buttonAnimationPhase,
            'buttonAnimationPhase',
            ButtonAnimationPhase.transitioningToRectangular,
          ),
          // Login completes successfully
          isA<AuthenticationState>()
              .having((state) => state.isLoading, 'isLoading', false)
              .having((state) => state.isLoginSuccess, 'isLoginSuccess', true)
              .having((state) => state.isButtonAnimating, 'isButtonAnimating', false)
              .having((state) => state.buttonAnimationPhase, 'buttonAnimationPhase', ButtonAnimationPhase.rectangular),
        ],
        wait: Duration(seconds: 4), // Wait for the complete animation sequence
      );
    });
  });
}
