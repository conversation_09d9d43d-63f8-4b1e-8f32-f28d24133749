import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_project/presentation/theme/bloc/theme_bloc.dart';

void main() {
  group('ThemeBloc', () {
    late SharedPreferences prefs;
    late ThemeBloc themeBloc;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
      themeBloc = ThemeBloc(prefs);
    });

    tearDown(() {
      themeBloc.close();
    });

    test('initial state should have isDarkMode as false', () {
      expect(themeBloc.state.isDarkMode, false);
    });

    blocTest<ThemeBloc, ThemeState>(
      'emits [ThemeState(isDarkMode: false)] when LoadThemeEvent is added and no preference exists',
      build: () => themeBloc,
      act: (bloc) => bloc.add(LoadThemeEvent()),
      expect: () => [const ThemeState(isDarkMode: false)],
    );

    blocTest<ThemeBloc, ThemeState>(
      'emits [ThemeState(isDarkMode: true)] when LoadThemeEvent is added and dark mode preference exists',
      build: () {
        prefs.setBool('isDarkMode', true);
        return ThemeBloc(prefs);
      },
      act: (bloc) => bloc.add(LoadThemeEvent()),
      expect: () => [const ThemeState(isDarkMode: true)],
    );

    blocTest<ThemeBloc, ThemeState>(
      'emits [ThemeState(isDarkMode: true)] when ToggleThemeEvent is added from light mode',
      build: () => themeBloc,
      act: (bloc) => bloc.add(ToggleThemeEvent()),
      expect: () => [const ThemeState(isDarkMode: true)],
      verify: (_) {
        expect(prefs.getBool('isDarkMode'), true);
      },
    );

    blocTest<ThemeBloc, ThemeState>(
      'emits [ThemeState(isDarkMode: false)] when ToggleThemeEvent is added from dark mode',
      build: () {
        themeBloc.emit(const ThemeState(isDarkMode: true));
        return themeBloc;
      },
      act: (bloc) => bloc.add(ToggleThemeEvent()),
      expect: () => [const ThemeState(isDarkMode: false)],
      verify: (_) {
        expect(prefs.getBool('isDarkMode'), false);
      },
    );

    blocTest<ThemeBloc, ThemeState>(
      'emits [ThemeState(isDarkMode: true)] when SetThemeEvent(true) is added',
      build: () => themeBloc,
      act: (bloc) => bloc.add(const SetThemeEvent(true)),
      expect: () => [const ThemeState(isDarkMode: true)],
      verify: (_) {
        expect(prefs.getBool('isDarkMode'), true);
      },
    );

    blocTest<ThemeBloc, ThemeState>(
      'emits [ThemeState(isDarkMode: false)] when SetThemeEvent(false) is added',
      build: () => themeBloc,
      act: (bloc) => bloc.add(const SetThemeEvent(false)),
      expect: () => [const ThemeState(isDarkMode: false)],
      verify: (_) {
        expect(prefs.getBool('isDarkMode'), false);
      },
    );
  });
}
