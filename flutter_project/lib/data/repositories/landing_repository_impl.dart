import 'package:dartz/dartz.dart';
import '../../domain/entities/feature_entity.dart';
import '../../domain/entities/pricing_plan_entity.dart';
import '../../domain/failures/landing_failure.dart';
import '../../domain/repositories/landing_repository.dart';
import '../datasources/landing_remote_datasource.dart';

/// Implementation of the landing page repository.
/// 
/// This class implements the domain repository interface and coordinates
/// with remote data sources for landing page content. It follows Clean
/// Architecture principles by implementing the domain contract while
/// handling data layer concerns like error handling and data transformation.
/// 
/// The repository is responsible for:
/// - Fetching landing page content from remote sources
/// - Handling network failures gracefully
/// - Converting data layer objects to domain entities
/// - Converting exceptions to domain failures
/// - Providing consistent data access interface
class LandingRepositoryImpl implements LandingRepository {
  final LandingRemoteDataSource _remoteDataSource;

  LandingRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<LandingFailure, List<FeatureEntity>>> getFeatures() async {
    try {
      // Fetch features from remote data source
      final featureDtos = await _remoteDataSource.getFeatures();
      
      // Convert DTOs to domain entities
      final features = featureDtos
          .map((dto) => dto.toEntity())
          .toList();
      
      return Right(features);
    } catch (e) {
      // Convert exceptions to domain failures
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<LandingFailure, List<PricingPlanEntity>>> getPricingPlans() async {
    try {
      // Fetch pricing plans from remote data source
      final pricingPlanDtos = await _remoteDataSource.getPricingPlans();
      
      // Convert DTOs to domain entities
      final pricingPlans = pricingPlanDtos
          .map((dto) => dto.toEntity())
          .toList();
      
      return Right(pricingPlans);
    } catch (e) {
      // Convert exceptions to domain failures
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<LandingFailure, bool>> subscribeToNewsletter(String email) async {
    try {
      // Attempt newsletter subscription
      final result = await _remoteDataSource.subscribeToNewsletter(email);
      return Right(result);
    } catch (e) {
      // Convert exceptions to domain failures
      return Left(_mapExceptionToFailure(e));
    }
  }

  /// Maps data layer exceptions to domain failures.
  /// 
  /// This method centralizes the conversion of various exceptions
  /// into appropriate domain failure types, maintaining the separation
  /// between data layer concerns and domain layer abstractions.
  LandingFailure _mapExceptionToFailure(dynamic exception) {
    final errorMessage = exception.toString().toLowerCase();
    
    // Network-related errors
    if (errorMessage.contains('network') || 
        errorMessage.contains('connection') ||
        errorMessage.contains('timeout')) {
      return NetworkFailure();
    }
    
    // Server-related errors
    if (errorMessage.contains('server') || 
        errorMessage.contains('500') ||
        errorMessage.contains('502') ||
        errorMessage.contains('503')) {
      return ServerFailure();
    }
    
    // Data loading errors
    if (errorMessage.contains('data') || 
        errorMessage.contains('load') ||
        errorMessage.contains('fetch')) {
      return DataLoadFailure();
    }
    
    // Subscription-related errors
    if (errorMessage.contains('subscription') || 
        errorMessage.contains('newsletter') ||
        errorMessage.contains('email')) {
      return SubscriptionFailure();
    }
    
    // Default to unknown failure
    return UnknownFailure(exception.toString());
  }
}
