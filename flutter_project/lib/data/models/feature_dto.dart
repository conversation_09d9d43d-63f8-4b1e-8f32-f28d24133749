import '../../domain/entities/feature_entity.dart';

/// Data Transfer Object for feature data.
/// 
/// This class handles data serialization/deserialization between
/// the data layer and external sources (APIs, databases, local storage).
/// It follows Clean Architecture principles by separating data concerns
/// from domain entities.
/// 
/// The DTO is responsible for:
/// - JSON serialization/deserialization
/// - Data validation and transformation
/// - Mapping between external data formats and domain entities
class FeatureDto {
  final String id;
  final String title;
  final String description;
  final String iconPath;

  FeatureDto({
    required this.id,
    required this.title,
    required this.description,
    required this.iconPath,
  });

  /// Creates a FeatureDto from JSON data.
  /// 
  /// Used when deserializing data from APIs or local storage.
  /// Handles type casting and provides default values for missing fields.
  factory FeatureDto.fromJson(Map<String, dynamic> json) {
    return FeatureDto(
      id: json['id'] as String? ?? '',
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      iconPath: json['icon_path'] as String? ?? '',
    );
  }

  /// Converts FeatureDto to JSON format.
  /// 
  /// Used when serializing data for APIs or local storage.
  /// Ensures consistent data format across different storage mechanisms.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'icon_path': iconPath,
    };
  }

  /// Converts FeatureDto to domain entity.
  /// 
  /// This is the key mapping function that transforms data layer objects
  /// into domain layer entities. It ensures that domain entities remain
  /// independent of external data formats.
  FeatureEntity toEntity() {
    return FeatureEntity(
      id: id,
      title: title,
      description: description,
      iconPath: iconPath,
    );
  }

  /// Creates FeatureDto from domain entity.
  /// 
  /// Used when converting domain entities back to data layer format
  /// for storage or API communication.
  factory FeatureDto.fromEntity(FeatureEntity entity) {
    return FeatureDto(
      id: entity.id,
      title: entity.title,
      description: entity.description,
      iconPath: entity.iconPath,
    );
  }

  /// Creates a copy of this FeatureDto with updated fields.
  /// 
  /// Useful for data transformations and updates without
  /// modifying the original object.
  FeatureDto copyWith({
    String? id,
    String? title,
    String? description,
    String? iconPath,
  }) {
    return FeatureDto(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      iconPath: iconPath ?? this.iconPath,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FeatureDto &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          title == other.title &&
          description == other.description &&
          iconPath == other.iconPath;

  @override
  int get hashCode =>
      id.hashCode ^
      title.hashCode ^
      description.hashCode ^
      iconPath.hashCode;

  @override
  String toString() {
    return 'FeatureDto{id: $id, title: $title, description: $description, iconPath: $iconPath}';
  }
}
