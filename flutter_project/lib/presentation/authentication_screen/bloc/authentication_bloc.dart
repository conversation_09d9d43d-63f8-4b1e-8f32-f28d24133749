import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../core/services/auth_validation_service.dart';
import '../../../domain/failures/auth_failure.dart';
import '../../../domain/usecases/auth/apple_login_usecase.dart';
import '../../../domain/usecases/auth/google_login_usecase.dart';
import '../../../domain/usecases/auth/login_usecase.dart';
import '../models/authentication_model.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

/// Manages authentication state including login validation and UI animations.
///
/// This BLoC handles user input validation, login button animations through
/// multiple phases (rectangular → circular → rectangular), and navigation
/// to the main app after successful authentication. It also manages password
/// visibility toggling and provides real-time validation feedback.
///
/// Key responsibilities:
/// - Email and password validation with real-time feedback
/// - Login button animation coordination (3-second sequence)
/// - Social login integration (Google, Apple)
/// - Error state management and user feedback
class AuthenticationBloc extends Bloc<AuthenticationEvent, AuthenticationState> {
  final LoginUseCase _loginUseCase;
  final GoogleLoginUseCase _googleLoginUseCase;
  final AppleLoginUseCase _appleLoginUseCase;

  AuthenticationBloc(
    super.initialState, {
    required LoginUseCase loginUseCase,
    required GoogleLoginUseCase googleLoginUseCase,
    required AppleLoginUseCase appleLoginUseCase,
  }) : _loginUseCase = loginUseCase,
       _googleLoginUseCase = googleLoginUseCase,
       _appleLoginUseCase = appleLoginUseCase {
    on<AuthenticationInitialEvent>(_onInitialize);
    on<EmailChangedEvent>(_onEmailChanged);
    on<PasswordChangedEvent>(_onPasswordChanged);
    on<TogglePasswordVisibilityEvent>(_onTogglePasswordVisibility);
    on<LoginButtonTappedEvent>(_onLoginButtonTapped);
    on<GoogleLoginTappedEvent>(_onGoogleLoginTapped);
    on<AppleLoginTappedEvent>(_onAppleLoginTapped);
    on<ForgotPasswordTappedEvent>(_onForgotPasswordTapped);
    on<SignUpTappedEvent>(_onSignUpTapped);
  }

  // Email validation method - delegates to service layer
  String? _validateEmail(String email) {
    return AuthValidationService.validateEmail(email);
  }

  // Password validation method - delegates to service layer
  String? _validatePassword(String password) {
    return AuthValidationService.validatePassword(password);
  }

  _onInitialize(AuthenticationInitialEvent event, Emitter<AuthenticationState> emit) async {
    emit(
      state.copyWith(
        emailController: TextEditingController()..text = '<EMAIL>',
        passwordController: TextEditingController()..text = 'password123',
        isPasswordVisible: false,
        isLoading: false,
        isLoginSuccess: false,
        showError: false,
        errorMessage: '',
        isButtonAnimating: false,
        buttonAnimationPhase: ButtonAnimationPhase.rectangular,
        clearEmailError: true, // Ensure errors are cleared on initialization
        clearPasswordError: true, // Ensure errors are cleared on initialization
      ),
    );
  }

  _onEmailChanged(EmailChangedEvent event, Emitter<AuthenticationState> emit) async {
    emit(
      state.copyWith(
        email: event.email,
        showError: false,
        clearEmailError: true, // Clear email error when user starts typing
      ),
    );
  }

  _onPasswordChanged(PasswordChangedEvent event, Emitter<AuthenticationState> emit) async {
    emit(
      state.copyWith(
        password: event.password,
        showError: false,
        clearPasswordError: true, // Clear password error when user starts typing
      ),
    );
  }

  _onTogglePasswordVisibility(TogglePasswordVisibilityEvent event, Emitter<AuthenticationState> emit) async {
    emit(state.copyWith(isPasswordVisible: !(state.isPasswordVisible ?? false)));
  }

  /// Handles login button tap with comprehensive validation and animation sequence.
  ///
  /// Orchestrates the complete login flow through 5 distinct phases:
  /// 1. Input validation (email format, password length)
  /// 2. Button animation start (rectangular → circular transition)
  /// 3. Authentication simulation (2.7 second delay)
  /// 4. Button animation completion (circular → rectangular transition)
  /// 5. Success state and navigation trigger
  ///
  /// The entire sequence takes approximately 3 seconds and provides visual
  /// feedback to the user throughout the process. If validation fails,
  /// the sequence is aborted and field-specific errors are displayed.
  _onLoginButtonTapped(LoginButtonTappedEvent event, Emitter<AuthenticationState> emit) async {
    // Step 1: Validate inputs
    if (!_validateLoginInputs(emit)) {
      return; // Early return if validation fails
    }

    // Step 2: Start button animation
    await _startLoginAnimation(emit);

    // Step 3: Perform authentication
    await _performAuthentication(emit);

    // Step 4: Complete button animation
    await _completeLoginAnimation(emit);

    // Step 5: Complete login process
    _completeLogin(emit);
  }

  /// Validates login inputs and emits validation errors if any
  /// Returns true if validation passes, false otherwise
  bool _validateLoginInputs(Emitter<AuthenticationState> emit) {
    final email = state.emailController?.text ?? '';
    final password = state.passwordController?.text ?? '';

    // Validate fields using validation methods
    final emailError = _validateEmail(email);
    final passwordError = _validatePassword(password);

    // If validation fails, show field-specific errors
    if (emailError != null || passwordError != null) {
      emit(
        state.copyWith(
          emailError: emailError,
          passwordError: passwordError,
          showError: false, // Don't show general error when we have field-specific errors
        ),
      );
      return false;
    }
    return true;
  }

  /// Starts the login button animation sequence
  Future<void> _startLoginAnimation(Emitter<AuthenticationState> emit) async {
    // Clear any existing errors and start loading with button animation
    emit(
      state.copyWith(
        isLoading: true,
        showError: false,
        isButtonAnimating: true,
        buttonAnimationPhase: ButtonAnimationPhase.transitioningToCircular,
        clearEmailError: true, // Explicitly clear email error
        clearPasswordError: true, // Explicitly clear password error
      ),
    );

    // Wait for button to transition to circular (300ms)
    await Future.delayed(Duration(milliseconds: 300));

    // Set button to circular state
    emit(state.copyWith(buttonAnimationPhase: ButtonAnimationPhase.circular));
  }

  /// Performs the authentication business logic using use case
  Future<void> _performAuthentication(Emitter<AuthenticationState> emit) async {
    final email = state.emailController?.text ?? '';
    final password = state.passwordController?.text ?? '';

    // Use login use case for authentication
    final result = await _loginUseCase(LoginParams(email: email, password: password));

    result.fold(
      (failure) {
        // Handle authentication failure
        String errorMessage = 'Login failed';
        Map<String, String> fieldErrors = {};

        if (failure is ValidationFailure) {
          fieldErrors = failure.errors;
          errorMessage = 'Please check your input';
        } else if (failure is AuthenticationFailure) {
          errorMessage = 'Invalid email or password';
        } else if (failure is NetworkFailure) {
          errorMessage = 'Network error. Please try again.';
        }

        emit(
          state.copyWith(
            isLoading: false,
            emailError: fieldErrors['email'],
            passwordError: fieldErrors['password'],
            errorMessage: fieldErrors.isEmpty ? errorMessage : null,
          ),
        );
      },
      (user) {
        // Handle successful authentication
        emit(state.copyWith(isLoading: false, isLoginSuccess: true));
      },
    );
  }

  /// Completes the login button animation sequence
  Future<void> _completeLoginAnimation(Emitter<AuthenticationState> emit) async {
    // Start button transition back to rectangular
    emit(state.copyWith(buttonAnimationPhase: ButtonAnimationPhase.transitioningToRectangular));

    // Wait for button to return to rectangular (300ms)
    await Future.delayed(Duration(milliseconds: 300));
  }

  /// Completes the login process and sets final success state
  void _completeLogin(Emitter<AuthenticationState> emit) {
    // Complete login and trigger navigation
    emit(
      state.copyWith(
        isLoading: false,
        isLoginSuccess: false,
        showError: false,
        isButtonAnimating: false,
        buttonAnimationPhase: ButtonAnimationPhase.rectangular,
      ),
    );
  }

  _onGoogleLoginTapped(GoogleLoginTappedEvent event, Emitter<AuthenticationState> emit) async {
    emit(state.copyWith(isLoading: true));

    // Use Google login use case
    final result = await _googleLoginUseCase();

    result.fold(
      (failure) => emit(state.copyWith(isLoading: false, errorMessage: 'Google login failed')),
      (user) => emit(state.copyWith(isLoading: false, isLoginSuccess: true)),
    );
  }

  _onAppleLoginTapped(AppleLoginTappedEvent event, Emitter<AuthenticationState> emit) async {
    emit(state.copyWith(isLoading: true));

    // Use Apple login use case
    final result = await _appleLoginUseCase();

    result.fold(
      (failure) => emit(state.copyWith(isLoading: false, errorMessage: 'Apple login failed')),
      (user) => emit(state.copyWith(isLoading: false, isLoginSuccess: true)),
    );
  }

  _onForgotPasswordTapped(ForgotPasswordTappedEvent event, Emitter<AuthenticationState> emit) async {
    // Handle forgot password logic
    // Navigate to forgot password screen or show dialog
  }

  _onSignUpTapped(SignUpTappedEvent event, Emitter<AuthenticationState> emit) async {
    // Handle sign up navigation
    // Navigate to sign up screen
  }

  @override
  Future<void> close() {
    // Dispose TextEditingControllers to prevent memory leaks
    state.emailController?.dispose();
    state.passwordController?.dispose();
    return super.close();
  }
}
