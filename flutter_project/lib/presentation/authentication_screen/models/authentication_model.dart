import '../../../core/app_export.dart';

/// This class is used in the [AuthenticationScreen] screen.

class AuthenticationModel extends Equatable {
  AuthenticationModel({
    String? illustrationImage,
    String? title,
    String? subtitle,
    String? emailLabel,
    String? passwordLabel,
    String? forgotPasswordText,
    String? loginButtonText,
    String? googleLoginText,
    String? appleLoginText,
    String? signUpPromptText,
    String? signUpLinkText,
  }) : illustrationImage = illustrationImage ?? ImageConstant.headerImgIllustration,
       title = title ?? 'Login to you account',
       subtitle = subtitle ?? 'Welcome back! Please enter your details.',
       emailLabel = emailLabel ?? 'Email',
       passwordLabel = passwordLabel ?? 'Password',
       forgotPasswordText = forgotPasswordText ?? 'Forgot password?',
       loginButtonText = loginButtonText ?? 'Log in',
       googleLoginText = googleLoginText ?? 'Log in with Google',
       appleLoginText = appleLoginText ?? 'Log in with Apple',
       signUpPromptText = signUpPromptText ?? 'Don\'t have an account?',
       signUpLinkText = signUpLinkText ?? 'Sign Up';

  final String? illustrationImage;
  final String? title;
  final String? subtitle;
  final String? emailLabel;
  final String? passwordLabel;
  final String? forgotPasswordText;
  final String? loginButtonText;
  final String? googleLoginText;
  final String? appleLoginText;
  final String? signUpPromptText;
  final String? signUpLinkText;

  AuthenticationModel copyWith({
    String? illustrationImage,
    String? title,
    String? subtitle,
    String? emailLabel,
    String? passwordLabel,
    String? forgotPasswordText,
    String? loginButtonText,
    String? googleLoginText,
    String? appleLoginText,
    String? signUpPromptText,
    String? signUpLinkText,
  }) {
    return AuthenticationModel(
      illustrationImage: illustrationImage ?? this.illustrationImage,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      emailLabel: emailLabel ?? this.emailLabel,
      passwordLabel: passwordLabel ?? this.passwordLabel,
      forgotPasswordText: forgotPasswordText ?? this.forgotPasswordText,
      loginButtonText: loginButtonText ?? this.loginButtonText,
      googleLoginText: googleLoginText ?? this.googleLoginText,
      appleLoginText: appleLoginText ?? this.appleLoginText,
      signUpPromptText: signUpPromptText ?? this.signUpPromptText,
      signUpLinkText: signUpLinkText ?? this.signUpLinkText,
    );
  }

  @override
  List<Object?> get props => [
    illustrationImage,
    title,
    subtitle,
    emailLabel,
    passwordLabel,
    forgotPasswordText,
    loginButtonText,
    googleLoginText,
    appleLoginText,
    signUpPromptText,
    signUpLinkText,
  ];
}
