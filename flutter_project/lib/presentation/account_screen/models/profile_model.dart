import '../../../core/app_export.dart';

class ProfileModel extends Equatable {
  final String? profileImage;
  final String? userName;
  final String? userId;
  final String? walletBalance;
  final List<ProfileMenuItemModel>? accountMenuItems;
  final List<ProfileMenuItemModel>? supportMenuItems;
  final List<ProfileMenuItemModel>? othersMenuItems;

  ProfileModel({
    String? profileImage,
    String? userName,
    String? userId,
    String? walletBalance,
    List<ProfileMenuItemModel>? accountMenuItems,
    List<ProfileMenuItemModel>? supportMenuItems,
    List<ProfileMenuItemModel>? othersMenuItems,
  }) : profileImage = profileImage ?? ImageConstant.profileImagePlaceholder,
       userName = userName ?? 'Olivia Rhye',
       userId = userId ?? 'OP8761',
       walletBalance = walletBalance ?? '50.00',
       accountMenuItems = accountMenuItems ?? [],
       supportMenuItems = supportMenuItems ?? [],
       othersMenuItems = othersMenuItems ?? [];

  @override
  List<Object?> get props => [profileImage, userName, userId, walletBalance, accountMenuItems, supportMenuItems, othersMenuItems];

  ProfileModel copyWith({
    String? profileImage,
    String? userName,
    String? userId,
    String? walletBalance,
    List<ProfileMenuItemModel>? accountMenuItems,
    List<ProfileMenuItemModel>? supportMenuItems,
    List<ProfileMenuItemModel>? othersMenuItems,
  }) {
    return ProfileModel(
      profileImage: profileImage ?? this.profileImage,
      userName: userName ?? this.userName,
      userId: userId ?? this.userId,
      walletBalance: walletBalance ?? this.walletBalance,
      accountMenuItems: accountMenuItems ?? this.accountMenuItems,
      supportMenuItems: supportMenuItems ?? this.supportMenuItems,
      othersMenuItems: othersMenuItems ?? this.othersMenuItems,
    );
  }
}

class ProfileMenuItemModel extends Equatable {
  final String? icon;
  final String? title;
  final String? subtitle;

  const ProfileMenuItemModel({String? icon, String? title, String? subtitle}) : icon = icon ?? '', title = title ?? '', subtitle = subtitle ?? '';

  @override
  List<Object?> get props => [icon, title, subtitle];

  ProfileMenuItemModel copyWith({String? icon, String? title, String? subtitle}) {
    return ProfileMenuItemModel(icon: icon ?? this.icon, title: title ?? this.title, subtitle: subtitle ?? this.subtitle);
  }
}
