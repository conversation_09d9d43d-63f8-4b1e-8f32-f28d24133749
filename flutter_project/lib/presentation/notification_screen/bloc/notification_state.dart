part of 'notification_bloc.dart';

class NotificationState extends Equatable {
  final NotificationModel? notificationModel;

  const NotificationState({
    this.notificationModel,
  });

  @override
  List<Object?> get props => [
        notificationModel,
      ];

  NotificationState copyWith({
    NotificationModel? notificationModel,
  }) {
    return NotificationState(
      notificationModel: notificationModel ?? this.notificationModel,
    );
  }
}
