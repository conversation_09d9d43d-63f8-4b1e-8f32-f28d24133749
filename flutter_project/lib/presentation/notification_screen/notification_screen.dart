import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './bloc/notification_bloc.dart';
import './models/notification_model.dart';

class NotificationScreen extends StatelessWidget {
  const NotificationScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<NotificationBloc>(
      create: (context) => NotificationBloc(NotificationState(notificationModel: NotificationModel()))..add(NotificationInitialEvent()),
      child: const NotificationScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, state) {
        return Container(
          color: Theme.of(context).colorScheme.surface,
          child: Center(child: Text('Notification Screen', style: TextStyleHelper.instance.headline24SemiBold)),
        );
      },
    );
  }
}
