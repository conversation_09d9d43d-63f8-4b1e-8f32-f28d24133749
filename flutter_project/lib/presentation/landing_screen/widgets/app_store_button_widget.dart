import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../core/widgets/custom_image_view.dart';

class AppStoreButtonWidget extends StatelessWidget {
  final Color backgroundColor;
  final Color borderColor;
  final String appleLogoPath;
  final String downloadTextPath;
  final String storeTextPath;
  final VoidCallback? onPressed;

  const AppStoreButtonWidget({
    super.key,
    required this.backgroundColor,
    required this.borderColor,
    required this.appleLogoPath,
    required this.downloadTextPath,
    required this.storeTextPath,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        height: 44.h,
        padding: EdgeInsets.symmetric(horizontal: 16.h),
        decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(color: borderColor, width: 1.h),
          borderRadius: BorderRadius.circular(7.h),
        ),
        child: Row(
          children: [
            CustomImageView(
              imagePath: appleLogoPath,
              height: appleLogoPath.contains('google') ? 28.h : 24.h,
              width: appleLogoPath.contains('google') ? 28.h : 20.h,
            ),
            SizedBox(width: 8.h),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomImageView(imagePath: downloadTextPath, height: 8.h),
                  SizedBox(height: 2.h),
                  CustomImageView(imagePath: storeTextPath, height: 16.h),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
