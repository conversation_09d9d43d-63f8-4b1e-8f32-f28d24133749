import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../core/utils/app_colors.dart';
import '../../../core/widgets/custom_image_view.dart';
import '../bloc/home_bloc.dart';
import 'app_store_button_widget.dart';

/// Hero section widget for the landing page
///
/// Displays the main headline, description, app store buttons, and hero image.
/// This is the primary call-to-action section that appears at the top of the landing page.
class HeroSectionWidget extends StatelessWidget {
  final HomeState state;

  const HeroSectionWidget({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 32.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Portfolio performance tracking made easy',
            style: TextStyleHelper.instance.display36SemiBold.copyWith(color: appTheme.colorFF0F1728, height: 1.2),
          ),
          SizedBox(height: 24.h),
          Text(
            'Designed by marketers, for marketers. Untitled gives you the guidance, data and innovation you need to become a better marketer.',
            style: TextStyleHelper.instance.headline18.copyWith(color: AppColors.textSecondary, height: 1.4),
          ),
          SizedBox(height: 32.h),
          Row(
            children: [
              Expanded(
                child: AppStoreButtonWidget(
                  backgroundColor: AppColors.textPrimary,
                  borderColor: AppColors.borderGray,
                  appleLogoPath: ImageConstant.imgAppleLogo,
                  downloadTextPath: ImageConstant.imgDownloadOnThe,
                  storeTextPath: ImageConstant.imgAppStore,
                  onPressed: () {
                    context.read<HomeBloc>().add(AppStoreButtonPressedEvent());
                  },
                ),
              ),
              SizedBox(width: 16.h),
              Expanded(
                child: AppStoreButtonWidget(
                  backgroundColor: AppColors.textPrimary,
                  borderColor: AppColors.borderGray,
                  appleLogoPath: ImageConstant.imgGooglePlayLogo,
                  downloadTextPath: ImageConstant.imgGetItOn,
                  storeTextPath: ImageConstant.imgGooglePlay,
                  onPressed: () {
                    context.read<HomeBloc>().add(PlayStoreButtonPressedEvent());
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 32.h),

          IntrinsicHeight(
            child: OverflowBox(
              maxWidth: MediaQuery.of(context).size.width,
              child: CustomImageView(imagePath: ImageConstant.phoneMockup1, fit: BoxFit.cover, height: 380.h),
            ),
          ),
        ],
      ),
    );
  }
}
