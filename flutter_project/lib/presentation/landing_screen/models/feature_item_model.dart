import '../../../core/app_export.dart';

class FeatureItemModel extends Equatable {
  const FeatureItemModel({this.icon = '', this.title = '', this.description = ''});

  final String icon;
  final String title;
  final String description;

  FeatureItemModel copyWith({String? icon, String? title, String? description}) {
    return FeatureItemModel(icon: icon ?? this.icon, title: title ?? this.title, description: description ?? this.description);
  }

  @override
  List<Object?> get props => [icon, title, description];
}
