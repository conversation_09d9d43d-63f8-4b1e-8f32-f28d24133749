part of 'profile_bloc.dart';

abstract class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

class ProfileInitialEvent extends ProfileEvent {}

class EditProfileImageEvent extends ProfileEvent {}

class NavigateToEditProfileEvent extends ProfileEvent {}

class NavigateToAddressEvent extends ProfileEvent {}

class NavigateToHistoryEvent extends ProfileEvent {}

class NavigateToComplainEvent extends ProfileEvent {}

class NavigateToReferralEvent extends ProfileEvent {}

class NavigateToAboutUsEvent extends ProfileEvent {}

class NavigateToSettingsEvent extends ProfileEvent {}

class NavigateToHelpSupportEvent extends ProfileEvent {}

class LogoutEvent extends ProfileEvent {}
