import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import './bloc/news_bloc.dart';
import './models/news_model.dart';

class NewsScreen extends StatelessWidget {
  const NewsScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<NewsBloc>(
      create: (context) => NewsBloc(NewsState(newsModel: NewsModel()))..add(NewsInitialEvent()),
      child: const NewsScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NewsBloc, NewsState>(
      builder: (context, state) {
        return Container(
          color: Theme.of(context).colorScheme.surface,
          child: Center(child: Text('News Screen', style: TextStyleHelper.instance.headline24SemiBold)),
        );
      },
    );
  }
}
