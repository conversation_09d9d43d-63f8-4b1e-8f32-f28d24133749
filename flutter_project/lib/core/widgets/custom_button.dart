import 'package:flutter/material.dart';

import '../app_export.dart';
import '../utils/app_dimensions.dart';
import 'custom_image_view.dart';

/// A reusable button component with customizable styling and behavior.
///
/// Supports two variants (primary and outlined), optional icons, and
/// configurable dimensions. Handles button animations and provides
/// consistent styling across the application using semantic dimensions
/// and theme-aware colors.
///
/// Features:
/// - Primary and outlined button variants
/// - Optional leading icon with automatic spacing
/// - Configurable width (full-width or content-based)
/// - Theme-aware colors and dimensions
/// - Disabled state support
/// - Consistent Material Design styling
class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant,
    this.iconPath,
    this.isFullWidth,
    this.height,
    this.backgroundColor,
    this.borderColor,
    this.textColor,
    this.borderRadius,
    this.isEnabled,
  });

  /// The button text to display
  final String text;

  /// Callback function when button is pressed
  final VoidCallback? onPressed;

  /// Button style variant
  final CustomButtonVariant? variant;

  /// Optional path to icon image
  final String? iconPath;

  /// Whether button should take full available width
  final bool? isFullWidth;

  /// Custom height for the button
  final double? height;

  /// Custom background color
  final Color? backgroundColor;

  /// Custom border color
  final Color? borderColor;

  /// Custom text color
  final Color? textColor;

  /// Custom border radius
  final double? borderRadius;

  /// Whether button is enabled or disabled
  final bool? isEnabled;

  @override
  Widget build(BuildContext context) {
    final effectiveVariant = variant ?? CustomButtonVariant.primary;
    final effectiveHeight = height ?? AppDimensions.buttonDefaultHeight.h;
    final effectiveIsFullWidth = isFullWidth ?? true;
    final effectiveIsEnabled = isEnabled ?? true;

    final buttonStyle = _getButtonStyle(effectiveVariant);
    final effectiveTextColor = textColor ?? _getTextColor(effectiveVariant);

    Widget buttonChild = _buildButtonContent(effectiveTextColor);

    if (effectiveVariant == CustomButtonVariant.outlined) {
      return SizedBox(
        width: effectiveIsFullWidth ? double.infinity : null,
        height: effectiveHeight,
        child: OutlinedButton(onPressed: effectiveIsEnabled ? onPressed : null, style: buttonStyle, child: buttonChild),
      );
    }

    return SizedBox(
      width: effectiveIsFullWidth ? double.infinity : null,
      height: effectiveHeight,
      child: ElevatedButton(onPressed: effectiveIsEnabled ? onPressed : null, style: buttonStyle, child: buttonChild),
    );
  }

  Widget _buildButtonContent(Color effectiveTextColor) {
    if (iconPath != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImageView(imagePath: iconPath!, height: AppDimensions.buttonIconSize.h, width: AppDimensions.buttonIconSize.h),
          SizedBox(width: AppDimensions.buttonIconSpacing.h),
          Text(text, style: TextStyleHelper.instance.title16Medium.copyWith(height: 1.25, color: effectiveTextColor)),
        ],
      );
    }

    return Text(text, style: TextStyleHelper.instance.title16Medium.copyWith(height: 1.25, color: effectiveTextColor));
  }

  ButtonStyle _getButtonStyle(CustomButtonVariant variant) {
    final effectiveBorderRadius = borderRadius ?? AppDimensions.buttonBorderRadius.h;

    switch (variant) {
      case CustomButtonVariant.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? appTheme.colorFF7E56,
          elevation: AppDimensions.buttonElevation,
          shadowColor: appTheme.color0D1018,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(effectiveBorderRadius),
            side: BorderSide(color: borderColor ?? appTheme.colorFF7E56, width: AppDimensions.buttonBorderWidth),
          ),
          padding: EdgeInsets.symmetric(horizontal: AppDimensions.buttonHorizontalPadding.h),
        );
      case CustomButtonVariant.outlined:
        return OutlinedButton.styleFrom(
          backgroundColor: backgroundColor ?? appTheme.whiteCustom,
          elevation: AppDimensions.buttonElevation,
          shadowColor: appTheme.color0D1018,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(effectiveBorderRadius)),
          side: BorderSide(color: borderColor ?? appTheme.colorFFCFD4, width: AppDimensions.buttonBorderWidth),
          padding: EdgeInsets.symmetric(horizontal: AppDimensions.buttonHorizontalPadding.h),
        );
    }
  }

  Color _getTextColor(CustomButtonVariant variant) {
    switch (variant) {
      case CustomButtonVariant.primary:
        return appTheme.whiteCustom;
      case CustomButtonVariant.outlined:
        return appTheme.colorFF1616;
    }
  }
}

/// Enum for button style variants
enum CustomButtonVariant {
  /// Primary button with colored background
  primary,

  /// Outlined button with white background and border
  outlined,
}
