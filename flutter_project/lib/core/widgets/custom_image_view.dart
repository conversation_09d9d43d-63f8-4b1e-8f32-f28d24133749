import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../app_export.dart';

extension ImageTypeExtension on String {
  ImageType get imageType {
    if (startsWith('http') || startsWith('https')) {
      if (endsWith('.svg')) {
        return ImageType.networkSvg;
      }
      return ImageType.network;
    } else if (endsWith('.svg')) {
      return ImageType.svg;
    } else if (startsWith('file://')) {
      return ImageType.file;
    } else {
      return ImageType.png;
    }
  }
}

enum ImageType { svg, png, network, networkSvg, file, unknown }

class CustomImageView extends StatelessWidget {
  const CustomImageView({
    super.key,
    this.imagePath,
    this.height,
    this.width,
    this.color,
    this.fit,
    this.alignment,
    this.onTap,
    this.radius,
    this.margin,
    this.border,
    this.placeHolder,
  });

  ///[imagePath] parameter for showing image, defaults to imgImageNotFound if null or empty
  final String? imagePath;

  /// Gets the effective image path, using default if imagePath is null or empty
  String get effectiveImagePath => (imagePath == null || imagePath!.isEmpty) ? ImageConstant.imgImageNotFound : imagePath!;

  final double? height;

  final double? width;

  final Color? color;

  final BoxFit? fit;

  final String? placeHolder;

  final Alignment? alignment;

  final VoidCallback? onTap;

  final EdgeInsetsGeometry? margin;

  final BorderRadius? radius;

  final BoxBorder? border;

  @override
  Widget build(BuildContext context) {
    return alignment != null ? Align(alignment: alignment!, child: _buildWidget()) : _buildWidget();
  }

  Widget _buildWidget() {
    return Padding(
      padding: margin ?? EdgeInsets.zero,
      child: InkWell(onTap: onTap, child: _buildCircleImage()),
    );
  }

  ///build the image with border radius
  _buildCircleImage() {
    if (radius != null) {
      return ClipRRect(borderRadius: radius ?? BorderRadius.zero, child: _buildImageWithBorder());
    } else {
      return _buildImageWithBorder();
    }
  }

  ///build the image with border and border radius style
  _buildImageWithBorder() {
    if (border != null) {
      return Container(
        decoration: BoxDecoration(border: border, borderRadius: radius),
        child: _buildImageView(),
      );
    } else {
      return _buildImageView();
    }
  }

  Widget _buildImageView() {
    switch (effectiveImagePath.imageType) {
      case ImageType.svg:
        return SizedBox(
          height: height,
          width: width,
          child: SvgPicture.asset(
            semanticsLabel: effectiveImagePath,
            effectiveImagePath,
            fit: fit ?? BoxFit.contain,
            colorFilter: color != null ? ColorFilter.mode(color ?? appTheme.transparentCustom, BlendMode.srcIn) : null,
          ),
        );
      case ImageType.file:
        return Image.file(File(effectiveImagePath), height: height, width: width, fit: fit ?? BoxFit.cover, color: color);
      case ImageType.networkSvg:
        return SizedBox(
          height: height,
          width: width,
          child: SvgPicture.network(
            semanticsLabel: effectiveImagePath,
            effectiveImagePath,
            fit: fit ?? BoxFit.contain,
            colorFilter: color != null ? ColorFilter.mode(color ?? appTheme.transparentCustom, BlendMode.srcIn) : null,
          ),
        );
      case ImageType.network:
        return CachedNetworkImage(
          height: height,
          width: width,
          fit: fit,
          imageUrl: effectiveImagePath,
          color: color,
          placeholder: (context, url) => SizedBox(
            height: 30,
            width: 30,
            child: LinearProgressIndicator(color: appTheme.grey200, backgroundColor: appTheme.grey100),
          ),
          errorWidget: (context, url, error) =>
              Image.asset(placeHolder ?? ImageConstant.imgImageNotFound, height: height, width: width, fit: fit ?? BoxFit.cover),
        );
      case ImageType.png:
      default:
        return Image.asset(effectiveImagePath, height: height, width: width, fit: fit ?? BoxFit.cover, color: color);
    }
  }
}
