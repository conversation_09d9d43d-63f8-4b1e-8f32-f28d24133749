import 'package:flutter/material.dart';

// Global theme helper instance
ThemeHelper themeHelperInstance = ThemeHelper();

// Theme-aware getters that respond to current theme mode
LightCodeColors get appTheme => themeHelperInstance.themeColor();
ThemeData get theme => themeHelperInstance.themeData();
ThemeData get lightTheme => themeHelperInstance.lightThemeData();
ThemeData get darkTheme => themeHelperInstance.darkThemeData();

/// Helper class for managing themes and colors.
class ThemeHelper {
  // Current theme mode - updated by the app when theme changes
  bool _isDarkMode = false;

  // A map of custom color themes supported by the app
  final Map<String, LightCodeColors> _supportedCustomColor = {'lightCode': LightCodeColors(), 'darkCode': DarkCodeColors()};

  // A map of color schemes supported by the app
  final Map<String, ColorScheme> _supportedColorScheme = {
    'lightCode': ColorSchemes.lightCodeColorScheme,
    'darkCode': ColorSchemes.darkCodeColorScheme,
  };

  /// Updates the current theme mode
  void updateThemeMode(bool isDarkMode) {
    _isDarkMode = isDarkMode;
  }

  /// Returns the current theme key based on dark mode state
  String get _currentThemeKey => _isDarkMode ? 'darkCode' : 'lightCode';

  /// Returns the colors for the current theme.
  LightCodeColors _getThemeColors() {
    return _supportedCustomColor[_currentThemeKey] ?? LightCodeColors();
  }

  /// Returns the current theme data.
  ThemeData _getThemeData() {
    var colorScheme = _supportedColorScheme[_currentThemeKey] ?? ColorSchemes.lightCodeColorScheme;
    return ThemeData(visualDensity: VisualDensity.standard, colorScheme: colorScheme);
  }

  /// Returns the colors for the current theme.
  LightCodeColors themeColor() => _getThemeColors();

  /// Returns the current theme data.
  ThemeData themeData() => _getThemeData();

  /// Returns the light theme data.
  ThemeData lightThemeData() {
    final colorScheme = ColorSchemes.lightCodeColorScheme;
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      brightness: Brightness.light,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        shadowColor: colorScheme.shadow,
      ),
      tabBarTheme: TabBarThemeData(
        labelColor: colorScheme.primary,
        unselectedLabelColor: Color(0xFF667085),
        indicatorColor: colorScheme.primary,
        dividerColor: Colors.transparent,
        labelStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        unselectedLabelStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      ),
    );
  }

  /// Returns the dark theme data.
  ThemeData darkThemeData() {
    final colorScheme = ColorSchemes.darkCodeColorScheme;
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      brightness: Brightness.dark,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        shadowColor: colorScheme.shadow,
      ),
      tabBarTheme: TabBarThemeData(
        labelColor: colorScheme.primary,
        unselectedLabelColor: Color(0xFFB3B3B3),
        indicatorColor: colorScheme.primary,
        dividerColor: Colors.transparent,
        labelStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        unselectedLabelStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      ),
    );
  }
}

class ColorSchemes {
  static final lightCodeColorScheme = ColorScheme.light(
    primary: Color(0xFF7E56D8),
    onPrimary: Color(0xFFFFFFFF),
    secondary: Color(0xFF52379E),
    onSecondary: Color(0xFFFFFFFF),
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF161616),
    error: Color(0xFFFF5252),
    onError: Color(0xFFFFFFFF),
  );

  static final darkCodeColorScheme = ColorScheme.dark(
    primary: Color(0xFF7E56D8),
    onPrimary: Color(0xFFFFFFFF),
    secondary: Color(0xFF52379E),
    onSecondary: Color(0xFFFFFFFF),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFE5E5E5),
    error: Color(0xFFFF5252),
    onError: Color(0xFFFFFFFF),
  );
}

class LightCodeColors {
  // App Colors
  Color get black => Color(0xFF1E1E1E);
  Color get white => Color(0xFFFFFFFF);
  Color get gray400 => Color(0xFF9CA3AF);

  // Additional Colors
  Color get whiteCustom => Colors.white;
  Color get redCustom => Colors.red;
  Color get transparentCustom => Colors.transparent;
  Color get greyCustom => Colors.grey;
  Color get blackCustom => Colors.black;

  // Semantic color aliases for better maintainability
  // These provide meaningful names while keeping hex-based names for backward compatibility
  Color get primaryText => colorFF1616;
  Color get secondaryText => colorFF5252;
  Color get brandPrimary => colorFF7E56;
  Color get borderDefault => colorFFCFD4;
  Color get backgroundLight => colorFFF4F4;
  Color get backgroundGray => colorFFE0E0;
  Color get colorFF1616 => Color(0xFF161616);
  Color get colorFF5252 => Color(0xFF525252);
  Color get colorFF7E56 => Color(0xFF7E56D8);
  Color get colorFFCFD4 => Color(0xFFCFD4DC);
  Color get colorFFF4F4 => Color(0xFFF4F4F4);
  Color get colorFFE0E0 => Color(0xFFE0E0E0);
  Color get color0D1018 => Color(0x0D101828);

  // New Color Constants
  Color get colorFFF9F5FF => Color(0xFFF9F5FF);
  Color get colorFF6840C6 => Color(0xFF6840C6);
  Color get colorFF0F1728 => Color(0xFF0F1728);
  Color get colorFF667084 => Color(0xFF667084);
  Color get colorFFA6A6A6 => Color(0xFFA6A6A6);
  Color get colorFFF8F9FB => Color(0xFFF8F9FB);
  Color get colorFF52379E => Color(0xFF52379E);
  Color get colorFFE9D7FE => Color(0xFFE9D7FE);
  Color get colorFFEAECF0 => Color(0xFFEAECF0);
  Color get colorFF422F7D => Color(0xFF422F7D);
  Color get colorFFF2F3F6 => Color(0xFFF2F3F6);
  Color get colorFFE5E7EB => Color(0xFFD0D5DD);
  Color get colorFF667085 => Color(0xFF667085);
  Color get colorFFF4EBFF => Color(0xFFF4EBFF);
  Color get colorFFD0FADF => Color(0xFFD0FADF);
  Color get colorFF101828 => Color(0xFF101828);

  // Color Shades - Each shade has its own dedicated constant
  Color get grey200 => Colors.grey.shade200;
  Color get grey100 => Colors.grey.shade100;
}

class DarkCodeColors extends LightCodeColors {
  // App Colors - Dark theme variants
  @override
  Color get black => Color(0xFFFFFFFF); // White text on dark background
  @override
  Color get white => Color(0xFF1E1E1E); // Dark background
  @override
  Color get gray400 => Color(0xFF6B7280);

  // Additional Colors - Dark theme variants
  @override
  Color get whiteCustom => Color(0xFF1E1E1E);
  @override
  Color get blackCustom => Color(0xFFFFFFFF);

  // Semantic color aliases for dark theme
  @override
  Color get primaryText => colorFF1616; // Points to dark variant
  @override
  Color get secondaryText => colorFF5252; // Points to dark variant
  @override
  Color get brandPrimary => colorFF7E56; // Points to dark variant
  @override
  Color get borderDefault => colorFFCFD4; // Points to dark variant
  @override
  Color get backgroundLight => colorFFF4F4; // Points to dark variant
  @override
  Color get backgroundGray => colorFFE0E0; // Points to dark variant
  @override
  Color get colorFF1616 => Color(0xFFE5E5E5);
  @override
  Color get colorFF5252 => Color(0xFFB3B3B3);
  @override
  Color get colorFF101828 => Color(0xFFE5E5E5);

  // Background colors for dark theme
  @override
  Color get colorFFF8F9FB => Color(0xFF2D2D2D);
  @override
  Color get colorFFF2F3F6 => Color(0xFF2A2A2A);
  @override
  Color get colorFFE5E7EB => Color(0xFF404040);
  @override
  Color get colorFFE0E0 => Color(0xFF404040);
  @override
  Color get colorFFCFD4 => Color(0xFF404040);
  @override
  Color get colorFFF4F4 => Color(0xFF2A2A2A);
  @override
  Color get colorFF667084 => Color(0xFFB3B3B3);
  @override
  Color get colorFFA6A6A6 => Color(0xFF6B7280);
  @override
  Color get colorFFEAECF0 => Color(0xFF404040);
  @override
  Color get colorFF667085 => Color(0xFFB3B3B3);
  @override
  Color get colorFF0F1728 => Color(0xFFE5E5E5);
  @override
  Color get colorFFF9F5FF => Color(0xFF2A2A2A);
  @override
  Color get colorFF6840C6 => Color(0xFF9B7EE8);
  @override
  Color get colorFF52379E => Color(0xFF7E56D8);
  @override
  Color get colorFFE9D7FE => Color(0xFF4A4A4A);

  // Color Shades - Dark theme variants
  @override
  Color get grey200 => Colors.grey.shade700;
  @override
  Color get grey100 => Colors.grey.shade800;
}
