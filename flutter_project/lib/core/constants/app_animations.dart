// lib/core/constants/app_animations.dart
import 'package:flutter/material.dart';

/// Animation duration and curve constants for the application
/// Provides semantic names for different animation types to improve maintainability
class AppAnimations {
  // Private constructor to prevent instantiation
  AppAnimations._();

  // Animation Durations
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration extraSlow = Duration(milliseconds: 800);

  // Specific Animation Durations
  static const Duration buttonTransition = Duration(milliseconds: 300);
  static const Duration pageTransition = Duration(milliseconds: 250);
  static const Duration dialogTransition = Duration(milliseconds: 200);
  static const Duration snackBarDuration = Duration(seconds: 2);
  static const Duration tooltipDuration = Duration(seconds: 1);
  static const Duration loadingIndicator = Duration(milliseconds: 1000);

  // Theme and UI Transitions
  static const Duration themeTransition = Duration(milliseconds: 400);
  static const Duration tabTransition = Duration(milliseconds: 200);
  static const Duration drawerTransition = Duration(milliseconds: 300);
  static const Duration bottomSheetTransition = Duration(milliseconds: 250);

  // Form and Input Animations
  static const Duration inputFieldFocus = Duration(milliseconds: 200);
  static const Duration validationMessage = Duration(milliseconds: 150);
  static const Duration formSubmission = Duration(milliseconds: 300);

  // List and Card Animations
  static const Duration listItemAnimation = Duration(milliseconds: 200);
  static const Duration cardFlip = Duration(milliseconds: 600);
  static const Duration cardExpansion = Duration(milliseconds: 300);

  // Animation Curves
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve fastCurve = Curves.easeOut;
  static const Curve slowCurve = Curves.easeInOutCubic;
  static const Curve bounceCurve = Curves.bounceOut;
  static const Curve elasticCurve = Curves.elasticOut;

  // Specific Animation Curves
  static const Curve buttonCurve = Curves.easeInOut;
  static const Curve pageCurve = Curves.easeInOutCubic;
  static const Curve dialogCurve = Curves.easeOut;
  static const Curve themeCurve = Curves.easeInOut;
}
