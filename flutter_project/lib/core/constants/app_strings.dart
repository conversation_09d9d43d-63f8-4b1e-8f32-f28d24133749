// lib/core/constants/app_strings.dart
class AppStrings {
  // Private constructor to prevent instantiation
  AppStrings._();

  // Landing Screen - Hero Section
  static const String personalizedCoachingInApp = 'Personalized coaching in-app';
  static const String personalPerformanceTrackingMadeEasy = 'Personal performance tracking made easy.';

  // Landing Screen - Partners Section
  static const String officialPartnerOfTheseCompanies = 'Official partner of these companies';

  // Landing Screen - Features Section
  static const String features = 'Features';
  static const String dailyPersonalizedFitnessDescription = 
      'Daily personalized fitness, sleep, and recovery data delivered to you in real time with Untitled. We\'re changing how you move.';

  // Landing Screen - Pricing Section
  static const String upgrade = 'Upgrade';
  static const String pricingPlansThatScaleWithYou = 'Pricing plans that scale with you';
  static const String simpleTransparentPricingDescription = 
      'Simple, transparent pricing that grows with you. Try any plan free for 30 days.';

  // Landing Screen - Newsletter Section
  static const String beTheFirstToKnowWhenWeLaunch = 'Be the first to know when we launch';
  static const String stillBuildingSubscribeDescription = 
      'We\'re still building. Subscribe for updates and 20% off when we launch.';
  static const String enterYourEmail = 'Enter your email';
  static const String subscribe = 'Subscribe';
  static const String weCareAboutYourDataIn = 'We care about your data in our ';
  static const String privacyPolicy = 'privacy policy';

  // Common UI Elements
  static const String mostPopular = 'Most popular!';
  static const String getStarted = 'Get started';
  static const String learnMore = 'Learn more';
  static const String tryForFree = 'Try for free';

  // Error Messages
  static const String somethingWentWrong = 'Something went wrong';
  static const String pleaseCheckYourConnection = 'Please check your internet connection';
  static const String invalidEmailFormat = 'Please enter a valid email address';

  // Success Messages
  static const String subscriptionSuccessful = 'Successfully subscribed!';
  static const String thankYouForSubscribing = 'Thank you for subscribing to our updates';
}
