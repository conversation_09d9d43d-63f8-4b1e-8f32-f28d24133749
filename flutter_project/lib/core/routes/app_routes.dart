import 'package:flutter/material.dart';

import '../../presentation/authentication_screen/authentication_screen.dart';
import '../../presentation/landing_screen/home_screen.dart';
import '../../presentation/main_navigation/main_navigation_screen.dart';
import '../../presentation/profile_screen/profile_screen.dart';

class AppRoutes {
  static const String authenticationScreen = '/authentication_screen';
  static const String profileScreen = '/profile_screen';
  static const String homeScreen = '/home_screen';
  static const String mainNavigationScreen = '/main_navigation_screen';
  static const String initialRoute = '/';

  static Map<String, WidgetBuilder> get routes => {
    authenticationScreen: AuthenticationScreen.builder,
    profileScreen: ProfileScreen.builder,
    homeScreen: HomeScreen.builder,
    mainNavigationScreen: MainNavigationScreen.builder,
    initialRoute: AuthenticationScreen.builder,
  };
}
