/// Domain entity representing a feature in the business logic layer.
///
/// This entity encapsulates the core feature data and business rules,
/// independent of any external concerns like UI or data storage.
/// Used in the landing page to display app features.
class FeatureEntity {
  final String id;
  final String title;
  final String description;
  final String iconPath;

  const FeatureEntity({required this.id, required this.title, required this.description, required this.iconPath});

  /// Creates a copy of this feature entity with updated fields
  FeatureEntity copyWith({String? id, String? title, String? description, String? iconPath}) {
    return FeatureEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      iconPath: iconPath ?? this.iconPath,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FeatureEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          title == other.title &&
          description == other.description &&
          iconPath == other.iconPath;

  @override
  int get hashCode => id.hashCode ^ title.hashCode ^ description.hashCode ^ iconPath.hashCode;

  @override
  String toString() {
    return 'FeatureEntity{id: $id, title: $title, description: $description, iconPath: $iconPath}';
  }
}
