/// Base class for landing page-related failures in the domain layer.
/// 
/// This follows Clean Architecture principles by defining domain-specific
/// error types that are independent of external frameworks or libraries.
/// All landing page failures inherit from this base class.
abstract class LandingFailure {
  final String message;
  const LandingFailure(this.message);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LandingFailure &&
          runtimeType == other.runtimeType &&
          message == other.message;

  @override
  int get hashCode => message.hashCode;

  @override
  String toString() => 'LandingFailure{message: $message}';
}

/// Failure that occurs when data loading fails.
/// 
/// This represents failures in loading features, pricing plans,
/// or other landing page data from external sources.
class DataLoadFailure extends LandingFailure {
  const DataLoadFailure() : super('Failed to load data');
}

/// Failure that occurs when newsletter subscription fails.
/// 
/// This represents business logic failures where the user's
/// email subscription request cannot be processed.
class SubscriptionFailure extends LandingFailure {
  const SubscriptionFailure() : super('Subscription failed');
}

/// Failure that occurs when there are network connectivity issues.
/// 
/// This represents infrastructure failures that prevent communication
/// with external services for landing page data.
class NetworkFailure extends LandingFailure {
  const NetworkFailure() : super('Network error occurred');
}

/// Failure that occurs when the server returns an unexpected error.
/// 
/// This represents server-side failures that prevent successful
/// data retrieval for the landing page.
class ServerFailure extends LandingFailure {
  const ServerFailure() : super('Server error occurred');
}

/// Failure that occurs for any other unexpected errors.
/// 
/// This is a catch-all failure type for errors that don't fit
/// into the other specific failure categories.
class UnknownFailure extends LandingFailure {
  const UnknownFailure(String message) : super(message);
}
