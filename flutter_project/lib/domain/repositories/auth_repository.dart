import 'package:dartz/dartz.dart';
import '../entities/user_entity.dart';
import '../failures/auth_failure.dart';

/// Repository interface for authentication operations in the domain layer.
/// 
/// This interface defines the contract for authentication data operations
/// following Clean Architecture principles. It's independent of any specific
/// implementation details and can be implemented by different data sources.
/// 
/// The repository pattern provides abstraction between the domain layer
/// and external concerns like APIs, databases, or local storage.
abstract class AuthRepository {
  /// Authenticates user with email and password credentials.
  /// 
  /// Returns [Right] with [UserEntity] on successful authentication,
  /// or [Left] with [AuthFailure] on failure.
  /// 
  /// Possible failures:
  /// - [ValidationFailure] if credentials are invalid format
  /// - [AuthenticationFailure] if credentials are rejected
  /// - [NetworkFailure] if network connectivity issues occur
  /// - [ServerFailure] if server returns unexpected error
  /// - [UnknownFailure] for any other unexpected errors
  Future<Either<AuthFailure, UserEntity>> authenticateWithCredentials(
    String email,
    String password,
  );

  /// Authenticates user with Google OAuth.
  /// 
  /// Returns [Right] with [UserEntity] on successful authentication,
  /// or [Left] with [AuthFailure] on failure.
  /// 
  /// Possible failures:
  /// - [AuthenticationFailure] if Google authentication is cancelled or fails
  /// - [NetworkFailure] if network connectivity issues occur
  /// - [ServerFailure] if server returns unexpected error
  /// - [UnknownFailure] for any other unexpected errors
  Future<Either<AuthFailure, UserEntity>> authenticateWithGoogle();

  /// Authenticates user with Apple ID.
  /// 
  /// Returns [Right] with [UserEntity] on successful authentication,
  /// or [Left] with [AuthFailure] on failure.
  /// 
  /// Possible failures:
  /// - [AuthenticationFailure] if Apple authentication is cancelled or fails
  /// - [NetworkFailure] if network connectivity issues occur
  /// - [ServerFailure] if server returns unexpected error
  /// - [UnknownFailure] for any other unexpected errors
  Future<Either<AuthFailure, UserEntity>> authenticateWithApple();

  /// Sends password reset email to the specified email address.
  /// 
  /// Returns [Right] with [true] if email was sent successfully,
  /// or [Left] with [AuthFailure] on failure.
  /// 
  /// Possible failures:
  /// - [ValidationFailure] if email format is invalid
  /// - [NetworkFailure] if network connectivity issues occur
  /// - [ServerFailure] if server returns unexpected error
  /// - [UnknownFailure] for any other unexpected errors
  Future<Either<AuthFailure, bool>> sendPasswordResetEmail(String email);

  /// Logs out the current user.
  /// 
  /// Returns [Right] with [true] if logout was successful,
  /// or [Left] with [AuthFailure] on failure.
  /// 
  /// Possible failures:
  /// - [NetworkFailure] if network connectivity issues occur
  /// - [ServerFailure] if server returns unexpected error
  /// - [UnknownFailure] for any other unexpected errors
  Future<Either<AuthFailure, bool>> logout();
}
