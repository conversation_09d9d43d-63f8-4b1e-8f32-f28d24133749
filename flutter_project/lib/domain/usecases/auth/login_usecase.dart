import 'package:dartz/dartz.dart';
import '../../entities/user_entity.dart';
import '../../failures/auth_failure.dart';
import '../../repositories/auth_repository.dart';
import '../../../core/services/auth_validation_service.dart';

/// Parameters for the login use case.
/// 
/// Encapsulates the input data required for user authentication
/// following Clean Architecture principles.
class LoginParams {
  final String email;
  final String password;

  const LoginParams({
    required this.email,
    required this.password,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginParams &&
          runtimeType == other.runtimeType &&
          email == other.email &&
          password == other.password;

  @override
  int get hashCode => email.hashCode ^ password.hashCode;

  @override
  String toString() => 'LoginParams{email: $email, password: [HIDDEN]}';
}

/// Use case interface for user login operations.
/// 
/// This interface defines the contract for login business logic
/// following Clean Architecture principles. It encapsulates the
/// business rules for user authentication.
abstract class LoginUseCase {
  /// Executes the login use case with the provided parameters.
  /// 
  /// Returns [Right] with [UserEntity] on successful authentication,
  /// or [Left] with [AuthFailure] on failure.
  Future<Either<AuthFailure, UserEntity>> call(LoginParams params);
}

/// Implementation of the login use case.
/// 
/// This class encapsulates the business logic for user authentication,
/// including input validation and delegation to the repository layer.
/// It follows Clean Architecture principles by being independent of
/// external frameworks and focusing purely on business rules.
class LoginUseCaseImpl implements LoginUseCase {
  final AuthRepository _authRepository;

  LoginUseCaseImpl(this._authRepository);

  @override
  Future<Either<AuthFailure, UserEntity>> call(LoginParams params) async {
    // Input validation using the existing validation service
    final emailError = AuthValidationService.validateEmail(params.email);
    final passwordError = AuthValidationService.validatePassword(params.password);

    // If validation fails, return validation failure
    if (emailError != null || passwordError != null) {
      final errors = <String, String>{};
      if (emailError != null) errors['email'] = emailError;
      if (passwordError != null) errors['password'] = passwordError;
      
      return Left(ValidationFailure(errors));
    }

    // Delegate to repository for authentication
    return await _authRepository.authenticateWithCredentials(
      params.email,
      params.password,
    );
  }
}
