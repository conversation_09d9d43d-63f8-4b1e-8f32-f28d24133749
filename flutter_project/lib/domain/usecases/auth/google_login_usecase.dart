import 'package:dartz/dartz.dart';
import '../../entities/user_entity.dart';
import '../../failures/auth_failure.dart';
import '../../repositories/auth_repository.dart';

/// Use case interface for Google login operations.
/// 
/// This interface defines the contract for Google authentication business logic
/// following Clean Architecture principles. It encapsulates the business rules
/// for Google OAuth authentication.
abstract class GoogleLoginUseCase {
  /// Executes the Google login use case.
  /// 
  /// Returns [Right] with [UserEntity] on successful authentication,
  /// or [Left] with [AuthFailure] on failure.
  Future<Either<AuthFailure, UserEntity>> call();
}

/// Implementation of the Google login use case.
/// 
/// This class encapsulates the business logic for Google OAuth authentication.
/// It follows Clean Architecture principles by being independent of external
/// frameworks and focusing purely on business rules.
/// 
/// The use case delegates the actual authentication to the repository layer
/// while handling any business-specific logic or validation.
class GoogleLoginUseCaseImpl implements GoogleLoginUseCase {
  final AuthRepository _authRepository;

  GoogleLoginUseCaseImpl(this._authRepository);

  @override
  Future<Either<AuthFailure, UserEntity>> call() async {
    // Delegate to repository for Google authentication
    // No additional business logic is required for Google login
    // as the validation is handled by the Google OAuth flow
    return await _authRepository.authenticateWithGoogle();
  }
}
