import 'package:dartz/dartz.dart';
import '../../entities/pricing_plan_entity.dart';
import '../../failures/landing_failure.dart';
import '../../repositories/landing_repository.dart';

/// Use case interface for retrieving pricing plans.
/// 
/// This interface defines the contract for pricing plan retrieval business logic
/// following Clean Architecture principles. It encapsulates the business rules
/// for loading and presenting pricing plans on the landing page.
abstract class GetPricingPlansUseCase {
  /// Executes the get pricing plans use case.
  /// 
  /// Returns [Right] with [List<PricingPlanEntity>] on successful data retrieval,
  /// or [Left] with [LandingFailure] on failure.
  Future<Either<LandingFailure, List<PricingPlanEntity>>> call();
}

/// Implementation of the get pricing plans use case.
/// 
/// This class encapsulates the business logic for retrieving pricing plans.
/// It follows Clean Architecture principles by being independent of external
/// frameworks and focusing purely on business rules.
/// 
/// The use case delegates the actual data retrieval to the repository layer
/// while handling any business-specific logic, validation, or transformation.
class GetPricingPlansUseCaseImpl implements GetPricingPlansUseCase {
  final LandingRepository _landingRepository;

  GetPricingPlansUseCaseImpl(this._landingRepository);

  @override
  Future<Either<LandingFailure, List<PricingPlanEntity>>> call() async {
    // Delegate to repository for pricing plan data retrieval
    final result = await _landingRepository.getPricingPlans();
    
    // Apply any business logic transformations if needed
    return result.fold(
      (failure) => Left(failure),
      (pricingPlans) {
        // Business rule: Ensure pricing plans are not empty
        if (pricingPlans.isEmpty) {
          return Left(DataLoadFailure());
        }
        
        // Business rule: Sort pricing plans with popular plans first
        final sortedPlans = List<PricingPlanEntity>.from(pricingPlans)
          ..sort((a, b) {
            // Popular plans come first
            if (a.isPopular && !b.isPopular) return -1;
            if (!a.isPopular && b.isPopular) return 1;
            
            // Then sort by price (extract numeric value)
            final priceA = _extractPrice(a.price);
            final priceB = _extractPrice(b.price);
            return priceA.compareTo(priceB);
          });
        
        return Right(sortedPlans);
      },
    );
  }

  /// Extracts numeric price value from price string for sorting.
  /// 
  /// Handles formats like "$10/mth", "$20/mth", etc.
  double _extractPrice(String priceString) {
    final regex = RegExp(r'\$(\d+)');
    final match = regex.firstMatch(priceString);
    if (match != null) {
      return double.tryParse(match.group(1) ?? '0') ?? 0.0;
    }
    return 0.0;
  }
}
